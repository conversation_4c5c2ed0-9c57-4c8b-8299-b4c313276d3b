// Custom Kanban Views Type Definitions
// Extensible architecture for future filter enhancements and column-level sorting

// Comparison operators for numeric and text filtering
export type ComparisonOperator = 'equal' | 'not_equal' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal';
export type EmptyOperator = 'empty' | 'not_empty';
export type TextOperator = 'contains' | 'not_contains' | 'equals' | 'not_equals';

// Custom field filter configuration
export interface CustomFieldFilter {
  fieldId: string;
  operator: EmptyOperator | TextOperator | ComparisonOperator;
  value?: string | number;
}

// Effort filter configuration
export interface EffortFilter {
  operator: EmptyOperator | ComparisonOperator;
  value?: number; // Hours
}

// Folder filter configuration
export interface FolderFilter {
  selectedFolders: string[]; // Folder IDs
  includeSubfolders: boolean;
}

// Column-specific filter configuration
export interface ColumnFilterConfig {
  // Location/folder filtering
  folderFilter?: FolderFilter;

  // Custom field filtering
  customFieldFilters: CustomFieldFilter[];

  // User filtering
  assigneeFilter: string[]; // User IDs
  ownerFilter: string[]; // User IDs

  // Effort filtering
  effortFilter?: EffortFilter;

  // Enable/disable column filtering
  enabled: boolean;
}

// Base filter configuration (extensible for future filter types)
export interface KanbanFilterConfig {
  // Current filter types
  selectedProject: string;
  selectedUsers: string[];
  selectedGroups: string[];
  selectedOwners: string[];
  dueDateFilter: 'all' | 'overdue' | 'today' | 'week' | 'month';

  // Extensible filter configurations for future enhancements
  customFilters: Record<string, any>; // For future custom filter types
  advancedFilters: Record<string, any>; // For future advanced filtering options

  // Future filter types can be added here without breaking existing views
  [key: string]: any;
}

// Column sort configuration (for column-level sorting)
export interface ColumnSortConfig {
  enabled: boolean;
  sortBy: 'none' | 'dueDate' | 'priority' | 'title' | 'created' | 'assignee' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
  customSortFields?: Record<string, any>; // For future custom sort fields
}

// Individual column configuration
export interface KanbanColumnConfig {
  id: string;
  title: string;
  color: string;
  visible: boolean;
  position?: number;

  // Sorting capabilities
  sortConfig: ColumnSortConfig;

  // Column-specific filtering
  filterConfig?: ColumnFilterConfig;

  // Extensible column settings
  customSettings?: Record<string, any>;
}

// Simplified column config for UI components
export interface ColumnConfig {
  id: string;
  title: string;
  color: string;
  sortBy: 'none' | 'dueDate' | 'priority' | 'title' | 'created';
  sortOrder: 'asc' | 'desc';

  // Column-specific filtering
  filterConfig?: ColumnFilterConfig;
}

// Overall column configuration
export interface KanbanColumnsConfig {
  columns: KanbanColumnConfig[];
  columnOrder: string[];
  
  // Global sorting options (for future enhancement)
  globalSortConfig: {
    enabled?: boolean;
    defaultSort?: string;
    customGlobalSort?: Record<string, any>;
  };
  
  // Future column management features
  [key: string]: any;
}

// Card display options
export interface CardDisplayOptions {
  showAssignee: boolean;
  showDueDate: boolean;
  showPriority: boolean;
  showTags: boolean;
  showDescription?: boolean;
  showCommentCount?: boolean;
  showSubtaskProgress?: boolean;
  
  // Future card display options
  customDisplayFields?: Record<string, boolean>;
}

// View settings configuration
export interface KanbanViewSettings {
  isCompactView: boolean;
  showTaskCount: boolean;
  showPriorityLabels: boolean;
  highlightOverdue: boolean;
  cardDisplayOptions: CardDisplayOptions;
  
  // Future view customizations
  futureSettings: Record<string, any>;
  
  // Extensible settings
  [key: string]: any;
}

// Main kanban view interface
export interface KanbanView {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  createdBy: string;
  
  // Extensible configurations
  filterConfig: KanbanFilterConfig;
  columnConfig: KanbanColumnsConfig;
  viewSettings: KanbanViewSettings;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  lastUsedAt: string;
  usageCount: number;
  version: number;
  updatedBy?: string;
  
  // Sharing information (populated when needed)
  isShared?: boolean;
  sharedBy?: string;
  permissionLevel?: 'read' | 'write' | 'admin';
}

// View sharing configuration
export interface KanbanViewShare {
  id: string;
  viewId: string;
  sharedWithUserId: string;
  sharedByUserId: string;
  permissionLevel: 'read' | 'write' | 'admin';
  sharedAt: string;
  lastAccessedAt?: string;
  accessCount: number;
}

// Input types for creating/updating views
export interface CreateKanbanViewInput {
  name: string;
  description?: string;
  filterConfig: KanbanFilterConfig;
  columnConfig: KanbanColumnsConfig;
  viewSettings: KanbanViewSettings;
}

export interface UpdateKanbanViewInput {
  name?: string;
  description?: string;
  filterConfig?: Partial<KanbanFilterConfig>;
  columnConfig?: Partial<KanbanColumnsConfig>;
  viewSettings?: Partial<KanbanViewSettings>;
}

// View sharing input
export interface ShareKanbanViewInput {
  viewId: string;
  userEmail: string; // User will input email, we'll resolve to userId
  permissionLevel: 'read' | 'write' | 'admin';
}

// View operation results
export interface KanbanViewOperationResult {
  success: boolean;
  view?: KanbanView;
  error?: string;
}

export interface KanbanViewShareResult {
  success: boolean;
  share?: KanbanViewShare;
  error?: string;
}

// View list with metadata
export interface KanbanViewList {
  ownedViews: KanbanView[];
  sharedViews: KanbanView[];
  defaultView?: KanbanView;
}

// View selector state
export interface ViewSelectorState {
  currentViewId: string | null;
  availableViews: KanbanViewList;
  isLoading: boolean;
  error?: string;
}

// Future enhancement types (placeholders for extensibility)
export interface FutureFilterType {
  id: string;
  type: string;
  config: Record<string, any>;
  enabled: boolean;
}

export interface FutureColumnFeature {
  id: string;
  feature: string;
  config: Record<string, any>;
  enabled: boolean;
}

// Export utility types
export type ViewPermission = 'read' | 'write' | 'admin';
export type FilterType = keyof KanbanFilterConfig;
export type SortField = 'title' | 'assignee' | 'dueDate' | 'priority' | 'createdAt' | 'updatedAt';
export type SortOrder = 'asc' | 'desc';

// Default configurations for new views
export const DEFAULT_FILTER_CONFIG: KanbanFilterConfig = {
  selectedProject: 'all',
  selectedUsers: [],
  selectedGroups: [],
  selectedOwners: [],
  dueDateFilter: 'all',
  customFilters: {},
  advancedFilters: {}
};

// Default column filter configuration
export const DEFAULT_COLUMN_FILTER_CONFIG: ColumnFilterConfig = {
  customFieldFilters: [],
  assigneeFilter: [],
  ownerFilter: [],
  enabled: false
};

export const DEFAULT_COLUMN_CONFIG: KanbanColumnsConfig = {
  columns: [
    {
      id: 'todo',
      title: 'To Do',
      color: 'bg-gray-100',
      visible: true,
      position: 0,
      sortConfig: { enabled: false },
      filterConfig: { ...DEFAULT_COLUMN_FILTER_CONFIG }
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      color: 'bg-blue-100',
      visible: true,
      position: 1,
      sortConfig: { enabled: false },
      filterConfig: { ...DEFAULT_COLUMN_FILTER_CONFIG }
    },
    {
      id: 'review',
      title: 'Review',
      color: 'bg-yellow-100',
      visible: true,
      position: 2,
      sortConfig: { enabled: false },
      filterConfig: { ...DEFAULT_COLUMN_FILTER_CONFIG }
    },
    {
      id: 'done',
      title: 'Done',
      color: 'bg-green-100',
      visible: true,
      position: 3,
      sortConfig: { enabled: false },
      filterConfig: { ...DEFAULT_COLUMN_FILTER_CONFIG }
    }
  ],
  columnOrder: ['todo', 'in-progress', 'review', 'done'],
  globalSortConfig: { enabled: false }
};

export const DEFAULT_VIEW_SETTINGS: KanbanViewSettings = {
  isCompactView: false,
  showTaskCount: true,
  showPriorityLabels: true,
  highlightOverdue: true,
  cardDisplayOptions: {
    showAssignee: true,
    showDueDate: true,
    showPriority: true,
    showTags: true
  },
  futureSettings: {}
};
