-- Fix for Recurring Tasks RLS Policies
-- This file fixes the Row Level Security policies for task_recurrences table
-- to ensure all users can see recurring task indicators for tasks they can view
--
-- ISSUE: Originally, users could only see recurrences they created, which meant
-- recurring task indicators were not visible to other team members
--
-- SOLUTION: Update policies to match task visibility and editing permissions
--
-- Run this file in your Supabase SQL editor if you have recurring tasks
-- that are not showing indicators for all users

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can view their own recurrences" ON task_recurrences;
DROP POLICY IF EXISTS "Users can create recurrences for their tasks" ON task_recurrences;
DROP POLICY IF EXISTS "Users can update their own recurrences" ON task_recurrences;
DROP POLICY IF EXISTS "Users can delete their own recurrences" ON task_recurrences;

-- Create new permissive policies that align with task permissions

-- SELECT: Users can view recurrences for any task they can see
CREATE POLICY "Users can view recurrences for visible tasks" ON task_recurrences
    FOR SELECT USING (
        task_id IN (SELECT id FROM tasks)
    );

-- INSERT: Users can create recurrences for tasks they can edit
CREATE POLICY "Users can create recurrences for editable tasks" ON task_recurrences
    FOR INSERT WITH CHECK (
        task_id IN (
            SELECT id FROM tasks WHERE 
            created_by = auth.uid() OR 
            assigned_user_id = auth.uid() OR 
            owner_id = auth.uid() OR 
            auth.uid()::text = ANY(assigned_users) OR 
            EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
        )
    );

-- UPDATE: Users can update recurrences for tasks they can edit
CREATE POLICY "Users can update recurrences for editable tasks" ON task_recurrences
    FOR UPDATE USING (
        task_id IN (
            SELECT id FROM tasks WHERE 
            created_by = auth.uid() OR 
            assigned_user_id = auth.uid() OR 
            owner_id = auth.uid() OR 
            auth.uid()::text = ANY(assigned_users) OR 
            EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
        )
    );

-- DELETE: Users can delete recurrences for tasks they can edit
CREATE POLICY "Users can delete recurrences for editable tasks" ON task_recurrences
    FOR DELETE USING (
        task_id IN (
            SELECT id FROM tasks WHERE 
            created_by = auth.uid() OR 
            assigned_user_id = auth.uid() OR 
            owner_id = auth.uid() OR 
            auth.uid()::text = ANY(assigned_users) OR 
            EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
        )
    );

-- Verification query - run this to check the policies are in place
-- SELECT policyname, cmd FROM pg_policies WHERE tablename = 'task_recurrences' ORDER BY cmd, policyname;
