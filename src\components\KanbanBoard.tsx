import React, { useState, useEffect } from 'react';
import { Task } from '../types';
import { Pencil, Trash2, ChevronRight, ChevronDown, Check, RotateCcw, LayoutGrid, List, Save } from 'lucide-react';
import TaskForm from './TaskForm';
import KanbanFilters from './KanbanFilters';
import KanbanViewSelector from './KanbanViewSelector';
import SaveViewDialog from './SaveViewDialog';
import ShareViewDialog from './ShareViewDialog';
import EditViewDialog from './EditViewDialog';
import ColumnHeaderMenu from './ColumnHeaderMenu';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { TaskCardRichText } from './RichTextDisplay';
import Avatar from './Avatar';
import { KanbanView, ColumnConfig, DEFAULT_COLUMN_FILTER_CONFIG } from '../types/kanbanViews';
import { applyColumnFilters } from '../utils/columnFilterUtils';

import { getPriorityBadge } from '../utils/statusUtils';
import { getTaskTitleClasses } from '../utils/dateUtils';

interface KanbanProps {
  tasks?: Task[];
  onTaskMove?: (taskId: string, newStatus: Task['status']) => void;
}

export default function KanbanBoard({ tasks: propTasks, onTaskMove }: KanbanProps) {
  const {
    tasks: storeTasks,
    columns,
    projects,
    folders,
    users,
    customFields,
    taskRecurrences,
    appliedViewConfig,
    selectedTreeNode,
    addTask,
    updateTask,
    deleteTask,
    moveTask,
    applyViewConfig,
    clearViewConfig
  } = useSupabaseStore();

  // Kanban view always shows all tasks (independent of tree navigation)
  const tasks = propTasks || storeTasks;
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<Task['status']>('todo');
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [isCompactView, setIsCompactView] = useState<boolean>(false);

  // Filter states
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedOwners, setSelectedOwners] = useState<string[]>([]);
  const [dueDateFilter, setDueDateFilter] = useState<'all' | 'overdue' | 'today' | 'week' | 'month'>('all');

  // View management dialogs
  const [showSaveViewDialog, setShowSaveViewDialog] = useState(false);
  const [showShareViewDialog, setShowShareViewDialog] = useState(false);
  const [showEditViewDialog, setShowEditViewDialog] = useState(false);
  const [selectedViewForAction, setSelectedViewForAction] = useState<KanbanView | null>(null);

  // Column configurations
  const [columnConfigs, setColumnConfigs] = useState<Record<string, ColumnConfig>>(() => {
    // Initialize with default configurations for each column
    const defaultConfigs: Record<string, ColumnConfig> = {};
    columns.forEach(column => {
      defaultConfigs[column.id] = {
        id: column.id,
        title: column.title,
        color: 'bg-gray-100',
        sortBy: 'none',
        sortOrder: 'asc',
        filterConfig: { ...DEFAULT_COLUMN_FILTER_CONFIG }
      };
    });
    return defaultConfigs;
  });

  // Column order state
  const [columnOrder, setColumnOrder] = useState<string[]>(() =>
    columns.map(col => col.id)
  );

  // Update column order when columns change (e.g., after loading from database)
  useEffect(() => {
    if (columns.length > 0 && columnOrder.length === 0) {
      setColumnOrder(columns.map(col => col.id));
    }
  }, [columns, columnOrder.length]);

  // Sync local filter state with applied view config
  useEffect(() => {
    if (appliedViewConfig) {
      console.log('Applying view config to kanban board:', appliedViewConfig);
      setSelectedProject(appliedViewConfig.selectedProject);
      setSelectedUsers(appliedViewConfig.selectedUsers);
      setSelectedGroups(appliedViewConfig.selectedGroups);
      setSelectedOwners(appliedViewConfig.selectedOwners);
      setDueDateFilter(appliedViewConfig.dueDateFilter);
      setIsCompactView(appliedViewConfig.isCompactView);

      // Apply column configurations if they exist in the view
      if (appliedViewConfig.columnConfigs) {
        setColumnConfigs(appliedViewConfig.columnConfigs);
      }

      // Apply column order if it exists in the view, otherwise reset to default
      if (appliedViewConfig.columnOrder) {
        setColumnOrder(appliedViewConfig.columnOrder);
      } else if (columns.length > 0) {
        // Reset to default column order when no custom order is saved (only if columns are loaded)
        setColumnOrder(columns.map(col => col.id));
      }
    }
  }, [appliedViewConfig]);

  // Handle column configuration changes
  const handleColumnConfigChange = (config: ColumnConfig) => {
    setColumnConfigs(prev => ({
      ...prev,
      [config.id]: config
    }));
  };

  // Handle column reordering
  const handleMoveColumnLeft = (columnId: string) => {
    setColumnOrder(prev => {
      const currentIndex = prev.indexOf(columnId);
      if (currentIndex <= 0) return prev; // Can't move further left

      const newOrder = [...prev];
      // Swap with the column to the left
      [newOrder[currentIndex - 1], newOrder[currentIndex]] = [newOrder[currentIndex], newOrder[currentIndex - 1]];
      return newOrder;
    });
  };

  const handleMoveColumnRight = (columnId: string) => {
    setColumnOrder(prev => {
      const currentIndex = prev.indexOf(columnId);
      if (currentIndex >= prev.length - 1) return prev; // Can't move further right

      const newOrder = [...prev];
      // Swap with the column to the right
      [newOrder[currentIndex], newOrder[currentIndex + 1]] = [newOrder[currentIndex + 1], newOrder[currentIndex]];
      return newOrder;
    });
  };

  // Sort tasks based on column configuration
  const sortTasksForColumn = (tasks: Task[], columnId: string): Task[] => {
    const config = columnConfigs[columnId];
    if (!config || config.sortBy === 'none') {
      return tasks;
    }

    return [...tasks].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (config.sortBy) {
        case 'dueDate':
          aValue = a.dueDate ? new Date(a.dueDate).getTime() : Infinity;
          bValue = b.dueDate ? new Date(b.dueDate).getTime() : Infinity;
          break;
        case 'priority':
          const priorityOrder = { low: 1, medium: 2, high: 3, urgent: 4 };
          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'created':
          // Use task ID as a proxy for creation order (newer IDs are typically larger)
          aValue = a.id;
          bValue = b.id;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return config.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return config.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // Helper function to check if a task has recurring schedule
  const hasRecurringSchedule = (taskId: string) => {
    return taskRecurrences.some(recurrence =>
      recurrence.taskId === taskId && recurrence.isActive
    );
  };

  // View management handlers
  const handleCreateView = () => {
    setShowSaveViewDialog(true);
  };

  const handleEditView = (view: KanbanView) => {
    setSelectedViewForAction(view);
    setShowEditViewDialog(true);
  };

  const handleShareView = (view: KanbanView) => {
    setSelectedViewForAction(view);
    setShowShareViewDialog(true);
  };

  const handleCloseDialogs = () => {
    setShowSaveViewDialog(false);
    setShowShareViewDialog(false);
    setShowEditViewDialog(false);
    setSelectedViewForAction(null);
  };

  const handleDragStart = (e: React.DragEvent, taskId: string) => {
    e.dataTransfer.setData('taskId', taskId);
  };

  const handleDrop = async (e: React.DragEvent, status: Task['status']) => {
    const taskId = e.dataTransfer.getData('taskId');
    try {
      if (onTaskMove) {
        onTaskMove(taskId, status);
      } else {
        await moveTask(taskId, status);
      }
    } catch (error) {
      console.error('Failed to move task:', error);
    }
  };

  const handleAddTask = (status: Task['status']) => {
    setSelectedStatus(status);
    setEditingTask(null);
    setShowTaskForm(true);
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setShowTaskForm(true);
  };

  const handleDeleteTask = async (taskId: string) => {
    if (confirm('Are you sure you want to delete this task?')) {
      try {
        await deleteTask(taskId);
      } catch (error) {
        console.error('Failed to delete task:', error);
        alert('Failed to delete task. Please try again.');
      }
    }
  };

  const handleTaskSubmit = async (taskData: Omit<Task, 'id'>) => {
    try {
      if (editingTask) {
        // Extract only the updatable fields for task update
        const { comments, history, durations, subtasks, ...updateData } = taskData;

        // Include custom field values in the update
        if (taskData.customFieldValues) {
          console.log('Custom field values to save:', taskData.customFieldValues);
        }

        await updateTask(editingTask.id, updateData);
      } else {
        // For new tasks, include custom field values and context
        const newTaskData = { ...taskData, status: selectedStatus };

        // Set project context if a project is selected
        const selectedProject = projects.find(p => p.id === selectedTreeNode);
        if (selectedProject) {
          newTaskData.projectId = selectedProject.id;
        } else {
          // Set folder context if a folder is selected
          const selectedFolder = folders.find(f => f.id === selectedTreeNode);
          if (selectedFolder) {
            newTaskData.folderId = selectedFolder.id;
            // Ensure projectId is not set when creating a task directly in a folder
            newTaskData.projectId = undefined;
          }
        }

        if (taskData.customFieldValues && Object.keys(taskData.customFieldValues).length > 0) {
          console.log('Custom field values for new task:', taskData.customFieldValues);
        }

        await addTask(newTaskData);
      }

      // Note: syncData() removed to prevent conflicts - local state updates should be sufficient

      setShowTaskForm(false);
      setEditingTask(null);
    } catch (error) {
      console.error('Failed to save task:', error);
      alert(`Failed to save task: ${error.message || 'Unknown error'}. Please try again.`);
    }
  };

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  // Clear all filters function
  const clearAllFilters = () => {
    setSelectedProject('all');
    setSelectedUsers([]);
    setSelectedGroups([]);
    setSelectedOwners([]);
    setDueDateFilter('all');
  };

  // Comprehensive filtering logic
  const filteredTasks = tasks.filter(task => {
    // Project filter
    if (selectedProject !== 'all' && task.projectId !== selectedProject) {
      return false;
    }

    // Assigned users filter
    if (selectedUsers.length > 0) {
      const hasMatchingUser = selectedUsers.some(userId =>
        task.assignedUsers?.includes(userId) || task.assignedUserId === userId
      );
      if (!hasMatchingUser) return false;
    }

    // User groups filter
    if (selectedGroups.length > 0) {
      const hasMatchingGroup = selectedGroups.some(groupId =>
        task.assignedGroups?.includes(groupId)
      );
      if (!hasMatchingGroup) return false;
    }

    // Owners filter
    if (selectedOwners.length > 0) {
      const hasMatchingOwner = selectedOwners.some(ownerId =>
        task.ownerId === ownerId
      );
      if (!hasMatchingOwner) return false;
    }

    // Due date filter
    if (dueDateFilter !== 'all' && task.dueDate) {
      const dueDate = new Date(task.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const weekFromNow = new Date(today);
      weekFromNow.setDate(weekFromNow.getDate() + 7);

      const monthFromNow = new Date(today);
      monthFromNow.setMonth(monthFromNow.getMonth() + 1);

      switch (dueDateFilter) {
        case 'overdue':
          if (dueDate >= today) return false;
          break;
        case 'today':
          if (dueDate < today || dueDate >= tomorrow) return false;
          break;
        case 'week':
          if (dueDate < today || dueDate >= weekFromNow) return false;
          break;
        case 'month':
          if (dueDate < today || dueDate >= monthFromNow) return false;
          break;
      }
    }

    return true;
  });

  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : userId;
  };

  const getUser = (userId: string) => {
    return users.find(u => u.id === userId);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 p-4 pb-2">
        {/* View Selector and Save Button */}
        <div className="flex items-center justify-between gap-4 mb-3">
          <KanbanViewSelector
            onCreateView={handleCreateView}
            onEditView={handleEditView}
            onShareView={handleShareView}
          />

          <button
            onClick={handleCreateView}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            title="Save current view configuration"
          >
            <Save className="w-4 h-4" />
            Save View
          </button>
        </div>

        {/* Filters and Controls */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex-1">
            <KanbanFilters
              selectedProject={selectedProject}
              setSelectedProject={setSelectedProject}
              selectedUsers={selectedUsers}
              setSelectedUsers={setSelectedUsers}
              selectedGroups={selectedGroups}
              setSelectedGroups={setSelectedGroups}
              selectedOwners={selectedOwners}
              setSelectedOwners={setSelectedOwners}
              dueDateFilter={dueDateFilter}
              setDueDateFilter={setDueDateFilter}
              onClearFilters={clearAllFilters}
            />
          </div>

          {/* Compact View Toggle */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsCompactView(!isCompactView)}
              className={`p-2 rounded-lg border transition-colors ${
                isCompactView
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
              }`}
              title={isCompactView ? 'Switch to normal view' : 'Switch to compact view'}
            >
              {isCompactView ? <List className="w-4 h-4" /> : <LayoutGrid className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-x-auto overflow-y-hidden">
        <div className="flex gap-4 h-full p-4 pt-2 min-w-max">
        {columnOrder.map((columnId) => {
          const column = columns.find(col => col.id === columnId);
          if (!column) return null;

          const { id, title, color } = column;
          const columnConfig = columnConfigs[id] || {
            id,
            title,
            color: 'bg-gray-100',
            sortBy: 'none',
            sortOrder: 'asc',
            filterConfig: { ...DEFAULT_COLUMN_FILTER_CONFIG }
          };
          const columnTasks = filteredTasks.filter((task) => task.status === id);

          // Apply column-specific filters
          const columnFilteredTasks = columnConfig.filterConfig
            ? applyColumnFilters(columnTasks, columnConfig.filterConfig, folders, customFields || [])
            : columnTasks;

          const sortedTasks = sortTasksForColumn(columnFilteredTasks, id);

          return (
          <div
            key={id}
            className="flex-shrink-0 w-80 h-full flex flex-col"
            onDragOver={(e) => e.preventDefault()}
            onDrop={(e) => handleDrop(e, id as Task['status'])}
          >
            <div className={`rounded-lg ${columnConfig.color} p-4 h-full flex flex-col`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{columnConfig.title}</h3>
                  <span className="bg-white bg-opacity-70 text-gray-700 text-xs font-medium px-2 py-1 rounded-full">
                    {sortedTasks.length}
                  </span>
                </div>
                <ColumnHeaderMenu
                  column={{ id, title: columnConfig.title, color: columnConfig.color }}
                  config={columnConfig}
                  onConfigChange={handleColumnConfigChange}
                  onMoveLeft={() => handleMoveColumnLeft(id)}
                  onMoveRight={() => handleMoveColumnRight(id)}
                  canMoveLeft={columnOrder.indexOf(id) > 0}
                  canMoveRight={columnOrder.indexOf(id) < columnOrder.length - 1}
                />
              </div>
              
              <div className={`flex-1 overflow-y-auto ${isCompactView ? 'space-y-2' : 'space-y-3'}`}>
                {sortedTasks
                  .map((task) => (
                    <div
                      key={task.id}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task.id)}
                      className={`bg-white rounded-lg shadow-sm cursor-move hover:shadow-md transition-shadow group ${
                        isCompactView ? 'p-2' : 'p-3'
                      }`}
                    >
                      {isCompactView ? (
                        /* Compact View Layout */
                        <div>
                          <div className="flex justify-between items-start">
                            <div className="flex items-start gap-2 flex-1">
                              {task.subtasks.length > 0 && (
                                <button
                                  onClick={() => toggleTaskExpansion(task.id)}
                                  className="p-1 hover:bg-gray-100 rounded"
                                >
                                  {expandedTasks.has(task.id) ? (
                                    <ChevronDown className="w-3 h-3" />
                                  ) : (
                                    <ChevronRight className="w-3 h-3" />
                                  )}
                                </button>
                              )}
                              <div className="flex-1 min-w-0">
                                {/* First row: Title and Priority */}
                                <div className="flex items-center gap-2 mb-1">
                                  {hasRecurringSchedule(task.id) && (
                                    <div title="This task has a recurring schedule">
                                      <RotateCcw className="w-3 h-3 text-blue-600" />
                                    </div>
                                  )}
                                  <h4 className={`${getTaskTitleClasses(task, 'font-medium text-sm')} flex-1 truncate`}>
                                    {task.title}
                                  </h4>
                                  {getPriorityBadge(task.priority)}
                                </div>

                                {/* Second row: Assignee and Due Date */}
                                <div className="flex items-center justify-between text-xs">
                                  <div className="flex items-center gap-1">
                                    {task.assignedUserId && (
                                      <div className="flex items-center gap-1">
                                        <Avatar
                                          src={getUser(task.assignedUserId)?.avatar}
                                          name={getUser(task.assignedUserId)?.name || task.assignedUserId}
                                          size="sm"
                                        />
                                        <span className="text-gray-600 truncate max-w-20">
                                          {getUser(task.assignedUserId)?.name || task.assignedUserId}
                                        </span>
                                      </div>
                                    )}
                                    {task.assignedUsers && task.assignedUsers.length > 0 && (
                                      <div className="flex items-center gap-1">
                                        {task.assignedUsers.slice(0, 2).map((userId) => (
                                          <Avatar
                                            key={userId}
                                            src={getUser(userId)?.avatar}
                                            name={getUser(userId)?.name || userId}
                                            size="sm"
                                          />
                                        ))}
                                        {task.assignedUsers.length > 2 && (
                                          <div className="w-4 h-4 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600">
                                            +{task.assignedUsers.length - 2}
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                  {task.dueDate && (
                                    <span className="text-gray-500 text-xs">
                                      {new Date(task.dueDate).toLocaleDateString()}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <button
                                onClick={() => handleEditTask(task)}
                                className="p-1 hover:bg-gray-100 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <Pencil className="w-3 h-3" />
                              </button>
                              <button
                                onClick={() => handleDeleteTask(task.id)}
                                className="p-1 hover:bg-red-50 rounded opacity-0 group-hover:opacity-100 transition-opacity text-red-600"
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            </div>
                          </div>

                          {/* Subtasks section for compact view */}
                          {expandedTasks.has(task.id) && task.subtasks.length > 0 && (
                            <div className="border-t px-2 py-1 space-y-1 mt-2">
                              {task.subtasks.map((subtask) => (
                                <div
                                  key={subtask.id}
                                  className="flex items-center gap-2 text-xs hover:bg-gray-50 p-1 rounded group/subtask"
                                >
                                  <button
                                    onClick={() => console.log('Update subtask:', subtask.id)}
                                    className={`w-3 h-3 rounded border flex items-center justify-center flex-shrink-0 ${
                                      subtask.completed ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
                                    }`}
                                  >
                                    {subtask.completed && <Check className="w-2 h-2 text-white" />}
                                  </button>
                                  <span className={`flex-1 truncate ${subtask.completed ? 'line-through text-gray-500' : ''}`}>
                                    {subtask.title}
                                    {subtask.dueDate && (
                                      <span className="ml-2 text-xs text-gray-400">
                                        {new Date(subtask.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                      </span>
                                    )}
                                  </span>
                                  <div className="opacity-0 group-hover/subtask:opacity-100 transition-opacity">
                                    <button
                                      onClick={() => handleEditTask(task)}
                                      className="p-1 hover:bg-gray-100 rounded"
                                    >
                                      <Pencil className="w-2 h-2" />
                                    </button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ) : (
                        /* Normal View Layout */
                        <div>
                          <div className="flex justify-between items-start">
                            <div className="flex items-start gap-2 flex-1">
                              {task.subtasks.length > 0 && (
                                <button
                                  onClick={() => toggleTaskExpansion(task.id)}
                                  className="p-1 hover:bg-gray-100 rounded mt-1"
                                >
                                  {expandedTasks.has(task.id) ? (
                                    <ChevronDown className="w-4 h-4" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4" />
                                  )}
                                </button>
                              )}
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  {hasRecurringSchedule(task.id) && (
                                    <div title="This task has a recurring schedule">
                                      <RotateCcw className="w-3 h-3 text-blue-600" />
                                    </div>
                                  )}
                                  <h4 className={`${getTaskTitleClasses(task, 'font-medium')} flex-1`}>{task.title}</h4>
                                  {getPriorityBadge(task.priority)}
                                </div>
                                <TaskCardRichText
                                  content={task.description}
                                  maxLength={55}
                                />
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <button
                                onClick={() => handleEditTask(task)}
                                className="p-1 hover:bg-gray-100 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <Pencil className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteTask(task.id)}
                                className="p-1 hover:bg-red-50 rounded opacity-0 group-hover:opacity-100 transition-opacity text-red-600"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>

                          {task.projectId && (
                            <div className="mt-2">
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                {projects.find(p => p.id === task.projectId)?.name}
                              </span>
                            </div>
                          )}

                          {task.assignedGroups && task.assignedGroups.length > 0 && (
                            <div className="flex gap-1 mt-2">
                              {task.assignedGroups.map((groupId) => (
                                <span
                                  key={groupId}
                                  className="px-2 py-0.5 bg-purple-100 text-purple-800 rounded text-xs"
                                >
                                  {groupId}
                                </span>
                              ))}
                            </div>
                          )}

                          <div className="flex gap-2 mt-2">
                            {task.tags.map((tag) => (
                              <span
                                key={tag}
                                className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>

                          {task.dueDate && (
                            <div className="text-xs text-gray-500 mt-2">
                              Due: {new Date(task.dueDate).toLocaleDateString()}
                            </div>
                          )}

                          {/* Assigned Users */}
                          {(task.assignedUserId || (task.assignedUsers && task.assignedUsers.length > 0)) && (
                            <div className="flex items-center gap-1 mt-2">
                              {task.assignedUserId && (
                                <Avatar
                                  src={getUser(task.assignedUserId)?.avatar}
                                  name={getUser(task.assignedUserId)?.name || task.assignedUserId}
                                  size="sm"
                                />
                              )}
                              {task.assignedUsers?.slice(0, 3).map((userId) => (
                                <Avatar
                                  key={userId}
                                  src={getUser(userId)?.avatar}
                                  name={getUser(userId)?.name || userId}
                                  size="sm"
                                />
                              ))}
                              {task.assignedUsers && task.assignedUsers.length > 3 && (
                                <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600">
                                  +{task.assignedUsers.length - 3}
                                </div>
                              )}
                            </div>
                          )}

                          {/* Subtasks section */}
                          {expandedTasks.has(task.id) && task.subtasks.length > 0 && (
                            <div className="border-t px-3 py-2 space-y-2 mt-2">
                              {task.subtasks.map((subtask) => (
                                <div
                                  key={subtask.id}
                                  className="flex items-center gap-2 text-sm hover:bg-gray-50 p-1 rounded group/subtask"
                                >
                                  <button
                                    onClick={() => console.log('Update subtask:', subtask.id)}
                                    className={`w-4 h-4 rounded border flex items-center justify-center flex-shrink-0 ${
                                      subtask.completed ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
                                    }`}
                                  >
                                    {subtask.completed && <Check className="w-3 h-3 text-white" />}
                                  </button>
                                  <span className={`flex-1 ${subtask.completed ? 'line-through text-gray-500' : ''}`}>
                                    {subtask.title}
                                    {subtask.dueDate && (
                                      <span className="ml-2 text-xs text-gray-400">
                                        {new Date(subtask.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                      </span>
                                    )}
                                  </span>
                                  {subtask.assignedUserId && (
                                    <span className="text-xs text-gray-500">
                                      {getUser(subtask.assignedUserId)?.name || subtask.assignedUserId}
                                    </span>
                                  )}
                                  <div className="opacity-0 group-hover/subtask:opacity-100 transition-opacity">
                                    <button
                                      onClick={() => handleEditTask(task)}
                                      className="p-1 hover:bg-gray-100 rounded"
                                    >
                                      <Pencil className="w-3 h-3" />
                                    </button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
          );
        })}
        </div>
      </div>

      {showTaskForm && (
        <TaskForm
          onSubmit={handleTaskSubmit}
          onClose={() => {
            setShowTaskForm(false);
            setEditingTask(null);
          }}
          initialData={editingTask || undefined}
        />
      )}

      {/* View Management Dialogs */}
      <SaveViewDialog
        isOpen={showSaveViewDialog}
        onClose={handleCloseDialogs}
        onSaved={handleCloseDialogs}
        currentState={{
          selectedProject,
          selectedUsers,
          selectedGroups,
          selectedOwners,
          dueDateFilter,
          isCompactView,
          columnConfigs,
          columnOrder
        }}
      />

      <ShareViewDialog
        isOpen={showShareViewDialog}
        view={selectedViewForAction}
        onClose={handleCloseDialogs}
        onShared={handleCloseDialogs}
      />

      <EditViewDialog
        isOpen={showEditViewDialog}
        view={selectedViewForAction}
        onClose={handleCloseDialogs}
        onSaved={handleCloseDialogs}
      />
    </div>
  );
}