import { Folder, Task, Project } from '../types';

/**
 * Recursively collect all subfolder IDs for a given folder
 * @param folderId - The parent folder ID to start from
 * @param folders - Array of all folders
 * @returns Array of all descendant folder IDs (including the parent folder ID)
 */
export function getAllSubfolderIds(folderId: string, folders: Folder[]): string[] {
  const result = new Set<string>();
  
  // Add the parent folder itself
  result.add(folderId);
  
  // Recursive function to collect subfolder IDs
  const collectSubfolders = (parentId: string) => {
    const subfolders = folders.filter(folder => folder.parentId === parentId);
    
    for (const subfolder of subfolders) {
      if (!result.has(subfolder.id)) {
        result.add(subfolder.id);
        // Recursively collect subfolders of this subfolder
        collectSubfolders(subfolder.id);
      }
    }
  };
  
  collectSubfolders(folderId);
  
  return Array.from(result);
}

/**
 * Get all tasks that belong to a folder and all its subfolders recursively
 * @param folderId - The folder ID to get tasks for
 * @param tasks - Array of all tasks
 * @param projects - Array of all projects
 * @param folders - Array of all folders
 * @returns Array of tasks in the folder hierarchy
 */
export function getTasksInFolderHierarchy(
  folderId: string,
  tasks: Task[],
  projects: Project[],
  folders: Folder[]
): Task[] {
  // Get all subfolder IDs recursively
  const allFolderIds = getAllSubfolderIds(folderId, folders);
  
  // Get all projects in these folders
  const projectsInFolders = projects.filter(project => 
    project.folderId && allFolderIds.includes(project.folderId)
  );
  const projectIds = projectsInFolders.map(project => project.id);
  
  // Filter tasks that are either:
  // 1. In projects within the folder hierarchy
  // 2. Directly in any of the folders in the hierarchy (without a project)
  return tasks.filter(task =>
    (task.projectId && projectIds.includes(task.projectId)) || // Tasks in projects within folders
    (task.folderId && allFolderIds.includes(task.folderId) && !task.projectId) // Tasks directly in folders
  );
}

/**
 * Check if a folder has any tasks (including in subfolders)
 * @param folderId - The folder ID to check
 * @param tasks - Array of all tasks
 * @param projects - Array of all projects
 * @param folders - Array of all folders
 * @returns True if the folder hierarchy contains any tasks
 */
export function folderHasAnyTasks(
  folderId: string,
  tasks: Task[],
  projects: Project[],
  folders: Folder[]
): boolean {
  const tasksInHierarchy = getTasksInFolderHierarchy(folderId, tasks, projects, folders);
  return tasksInHierarchy.length > 0;
}

/**
 * Get the depth level of a folder in the hierarchy
 * @param folderId - The folder ID
 * @param folders - Array of all folders
 * @returns The depth level (0 for root folders)
 */
export function getFolderDepth(folderId: string, folders: Folder[]): number {
  const folder = folders.find(f => f.id === folderId);
  if (!folder || !folder.parentId) {
    return 0;
  }
  
  return 1 + getFolderDepth(folder.parentId, folders);
}

/**
 * Get all parent folder IDs for a given folder (path to root)
 * @param folderId - The folder ID
 * @param folders - Array of all folders
 * @returns Array of parent folder IDs from immediate parent to root
 */
export function getFolderPath(folderId: string, folders: Folder[]): string[] {
  const path: string[] = [];
  let currentFolder = folders.find(f => f.id === folderId);
  
  while (currentFolder && currentFolder.parentId) {
    path.unshift(currentFolder.parentId);
    currentFolder = folders.find(f => f.id === currentFolder!.parentId);
  }
  
  return path;
}
