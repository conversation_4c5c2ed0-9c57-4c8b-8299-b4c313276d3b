import React, { useState, useEffect, useRef } from 'react';
import { Plus, ChevronDown, Folder as FolderIcon, FileText } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import TaskTreeNode from './TaskTreeNode';
import TaskForm from './TaskForm';
import ProjectForm from './ProjectForm';
import FolderForm from './FolderForm';
import { Task, Project, Folder } from '../types';
import { validateMoveOperation, generateMoveOperations, MoveOperation } from '../utils/treeUtils';

export default function TaskTreeSidebar() {
  const {
    projects,
    folders,
    tasks,
    selectedTreeNode,
    listViewSelectedTreeNode,
    tasksViewMode,
    expandedTreeNodes,
    setSelectedTreeNode,
    setListViewSelectedTreeNode,
    toggleTreeNode,
    addTask,
    updateTask,
    addProject,
    addFolder,
    updateProject,
    updateFolder,
    cloneProject,
    deleteProject,
    deleteFolder,
    moveFolder,
    moveProject,
    moveTaskToContainer
  } = useSupabaseStore();

  const [showTaskForm, setShowTaskForm] = useState(false);
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [showFolderForm, setShowFolderForm] = useState(false);
  const [contextMenuTarget, setContextMenuTarget] = useState<string | null>(null);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editingFolder, setEditingFolder] = useState<Folder | null>(null);
  const [showAddMenu, setShowAddMenu] = useState(false);
  const addMenuRef = useRef<HTMLDivElement>(null);

  // Helper functions to get appropriate context based on view mode
  const getCurrentSelectedNode = () => {
    return tasksViewMode === 'list' ? listViewSelectedTreeNode : selectedTreeNode;
  };

  const setCurrentSelectedNode = (nodeId: string | null) => {
    if (tasksViewMode === 'list') {
      setListViewSelectedTreeNode(nodeId);
    } else {
      setSelectedTreeNode(nodeId);
    }
  };

  // Drag and drop state
  const [draggedItem, setDraggedItem] = useState<{
    id: string;
    type: 'folder' | 'project' | 'task';
  } | null>(null);
  const [dragOverItem, setDragOverItem] = useState<string | null>(null);
  const [dropPosition, setDropPosition] = useState<'before' | 'after' | 'inside' | null>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (addMenuRef.current && !addMenuRef.current.contains(event.target as Node)) {
        setShowAddMenu(false);
      }
    };

    if (showAddMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAddMenu]);

  // Build hierarchical structure
  const rootFolders = folders.filter(f => !f.parentId);
  const rootProjects = projects.filter(p => !p.folderId);

  const getChildFolders = (parentId: string) => 
    folders.filter(f => f.parentId === parentId);
  
  const getProjectsInFolder = (folderId: string) => {
    const projectsInFolder = projects.filter(p => p.folderId === folderId);
    return projectsInFolder;
  };

  const getTasksInProject = (projectId: string) =>
    tasks.filter(t => t.projectId === projectId);

  // Drag and drop handlers
  const handleDragStart = (id: string, type: 'folder' | 'project' | 'task') => {
    setDraggedItem({ id, type });
  };

  const handleDragOver = (e: React.DragEvent, targetId: string, targetType: 'folder' | 'project' | 'task') => {
    e.preventDefault();
    e.stopPropagation();

    if (!draggedItem || draggedItem.id === targetId) return;

    setDragOverItem(targetId);

    // Determine drop position based on mouse position
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    if (targetType === 'folder') {
      // For folders, allow dropping inside
      if (y < height * 0.25) {
        setDropPosition('before');
      } else if (y > height * 0.75) {
        setDropPosition('after');
      } else {
        setDropPosition('inside');
      }
    } else {
      // For projects and tasks, only allow before/after
      if (y < height * 0.5) {
        setDropPosition('before');
      } else {
        setDropPosition('after');
      }
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    // Only clear if we're leaving the entire tree area
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragOverItem(null);
      setDropPosition(null);
    }
  };

  const handleDrop = async (e: React.DragEvent, targetId: string, targetType: 'folder' | 'project' | 'task') => {
    e.preventDefault();
    e.stopPropagation();

    if (!draggedItem || !dropPosition) return;

    try {
      const moveOperation: MoveOperation = {
        draggedItem,
        targetId,
        targetType,
        position: dropPosition
      };

      // Validate the move operation
      const validation = validateMoveOperation(moveOperation, folders, projects, tasks);
      if (!validation.isValid) {
        alert(validation.error || 'Invalid move operation');
        return;
      }

      // Generate and execute move operations
      const operations = generateMoveOperations(moveOperation, folders, projects, tasks);

      for (const operation of operations) {
        switch (operation.type) {
          case 'folder':
            await moveFolder(operation.id, operation.updates.parentId);
            break;
          case 'project':
            await moveProject(operation.id, operation.updates.folderId);
            break;
          case 'task':
            await moveTaskToContainer(
              operation.id,
              operation.updates.folderId,
              operation.updates.projectId
            );
            break;
        }
      }

      console.log('Move completed successfully');
    } catch (error) {
      console.error('Failed to move item:', error);
      alert('Failed to move item. Please try again.');
    } finally {
      // Clear drag state
      setDraggedItem(null);
      setDragOverItem(null);
      setDropPosition(null);
    }
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverItem(null);
    setDropPosition(null);
  };

  const handleAddProject = () => {
    setContextMenuTarget(null);
    setShowProjectForm(true);
  };

  const handleAddFolder = () => {
    setContextMenuTarget(null);
    setShowFolderForm(true);
  };

  const handleAddTask = (projectId?: string) => {
    setContextMenuTarget(projectId || null);
    setShowTaskForm(true);
  };

  const handleCloneProject = async (projectId: string) => {
    try {
      await cloneProject(projectId);
    } catch (error) {
      console.error('Failed to clone project:', error);
      alert('Failed to clone project. Please try again.');
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    try {
      await deleteFolder(folderId);
    } catch (error) {
      console.error('Failed to delete folder:', error);
      alert('Failed to delete folder. Please try again.');
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      await deleteProject(projectId);
    } catch (error) {
      console.error('Failed to delete project:', error);
      alert('Failed to delete project. Please try again.');
    }
  };

  const handleContextAction = (nodeId: string, action: 'addTask' | 'addProject' | 'addFolder' | 'editProject' | 'editFolder' | 'cloneProject' | 'deleteFolder' | 'deleteProject') => {
    switch (action) {
      case 'addTask':
        // Only allow adding tasks to projects
        if (projects.find(p => p.id === nodeId)) {
          handleAddTask(nodeId);
        }
        break;
      case 'addProject':
        // Add project to folder or root
        setContextMenuTarget(folders.find(f => f.id === nodeId) ? nodeId : null);
        setShowProjectForm(true);
        break;
      case 'addFolder':
        // Add subfolder to folder or root
        setContextMenuTarget(folders.find(f => f.id === nodeId) ? nodeId : null);
        setShowFolderForm(true);
        break;
      case 'editProject': {
        // Edit existing project
        const project = projects.find(p => p.id === nodeId);
        if (project) {
          setEditingProject(project);
          setShowProjectForm(true);
        }
        break;
      }
      case 'editFolder': {
        // Edit existing folder
        const folder = folders.find(f => f.id === nodeId);
        if (folder) {
          setEditingFolder(folder);
          setShowFolderForm(true);
        }
        break;
      }
      case 'cloneProject': {
        // Clone existing project
        const project = projects.find(p => p.id === nodeId);
        if (project) {
          handleCloneProject(nodeId);
        }
        break;
      }
      case 'deleteFolder': {
        // Delete folder with confirmation
        const folder = folders.find(f => f.id === nodeId);
        if (folder && window.confirm(`Are you sure you want to delete the folder "${folder.name}" and all its contents? This action will archive the folder and it can be restored by an admin within 1 week.`)) {
          handleDeleteFolder(nodeId);
        }
        break;
      }
      case 'deleteProject': {
        // Delete project with confirmation
        const project = projects.find(p => p.id === nodeId);
        if (project && window.confirm(`Are you sure you want to delete the project "${project.name}" and all its tasks? This action will archive the project and it can be restored by an admin within 1 week.`)) {
          handleDeleteProject(nodeId);
        }
        break;
      }
    }
  };

  const renderFolderContent = (folderId: string, level: number) => {
    const childFolders = getChildFolders(folderId);
    const projectsInFolder = getProjectsInFolder(folderId);
    
    return (
      <>
        {/* Child folders */}
        {childFolders.map(childFolder => (
          <TaskTreeNode
            key={childFolder.id}
            id={childFolder.id}
            type="folder"
            name={childFolder.name}
            level={level}
            isExpanded={expandedTreeNodes.has(childFolder.id)}
            isSelected={getCurrentSelectedNode() === childFolder.id}
            onToggle={() => toggleTreeNode(childFolder.id)}
            onSelect={() => setCurrentSelectedNode(childFolder.id)}
            onContextAction={handleContextAction}
            isDragging={draggedItem?.id === childFolder.id}
            isDragOver={dragOverItem === childFolder.id}
            dropPosition={dragOverItem === childFolder.id ? dropPosition : null}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onDragEnd={handleDragEnd}
          >
            {expandedTreeNodes.has(childFolder.id) && renderFolderContent(childFolder.id, level + 1)}
          </TaskTreeNode>
        ))}
        
        {/* Projects in this folder */}
        {projectsInFolder.map(project => (
          <TaskTreeNode
            key={project.id}
            id={project.id}
            type="project"
            name={project.name}
            level={level}
            color={project.color}
            isExpanded={expandedTreeNodes.has(project.id)}
            isSelected={getCurrentSelectedNode() === project.id}
            onToggle={() => toggleTreeNode(project.id)}
            onSelect={() => setCurrentSelectedNode(project.id)}
            onContextAction={handleContextAction}
            isDragging={draggedItem?.id === project.id}
            isDragOver={dragOverItem === project.id}
            dropPosition={dragOverItem === project.id ? dropPosition : null}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onDragEnd={handleDragEnd}
          >
            {expandedTreeNodes.has(project.id) && getTasksInProject(project.id).map(task => (
              <TaskTreeNode
                key={task.id}
                id={task.id}
                type="task"
                name={task.title}
                level={level + 1}
                status={task.status}
                isSelected={getCurrentSelectedNode() === task.id}
                onSelect={() => setCurrentSelectedNode(task.id)}
                onDoubleClick={() => {
                  setEditingTask(task);
                  setShowTaskForm(true);
                }}
                isDragging={draggedItem?.id === task.id}
                isDragOver={dragOverItem === task.id}
                dropPosition={dragOverItem === task.id ? dropPosition : null}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onDragEnd={handleDragEnd}
              />
            ))}
          </TaskTreeNode>
        ))}
      </>
    );
  };

  return (
    <>
      <div className="w-full h-full bg-gray-800 text-white overflow-y-auto">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">Projects</h2>
            <div className="relative" ref={addMenuRef}>
              <button
                onClick={() => setShowAddMenu(!showAddMenu)}
                className="p-1 hover:bg-gray-700 rounded text-white flex items-center gap-1"
                title="Add Project or Folder"
              >
                <Plus className="w-4 h-4" />
                <ChevronDown className="w-3 h-3" />
              </button>

              {showAddMenu && (
                <div className="absolute right-0 top-8 bg-gray-700 border border-gray-600 rounded-lg shadow-lg py-1 z-50 min-w-[140px]">
                  <button
                    onClick={() => {
                      handleAddProject();
                      setShowAddMenu(false);
                    }}
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-600 flex items-center gap-2 text-white"
                  >
                    <FileText className="w-3 h-3" />
                    Add Project
                  </button>
                  <button
                    onClick={() => {
                      handleAddFolder();
                      setShowAddMenu(false);
                    }}
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-600 flex items-center gap-2 text-white"
                  >
                    <FolderIcon className="w-3 h-3" />
                    Add Folder
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Tree container with horizontal scroll support */}
          <div className="overflow-x-auto overflow-y-visible">
            <div className="space-y-1 min-w-max">
            {/* Root folders */}
            {rootFolders.map(folder => (
              <TaskTreeNode
                key={folder.id}
                id={folder.id}
                type="folder"
                name={folder.name}
                level={0}
                isExpanded={expandedTreeNodes.has(folder.id)}
                isSelected={getCurrentSelectedNode() === folder.id}
                onToggle={() => toggleTreeNode(folder.id)}
                onSelect={() => setCurrentSelectedNode(folder.id)}
                onContextAction={handleContextAction}
                isDragging={draggedItem?.id === folder.id}
                isDragOver={dragOverItem === folder.id}
                dropPosition={dragOverItem === folder.id ? dropPosition : null}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onDragEnd={handleDragEnd}
              >
                {expandedTreeNodes.has(folder.id) && renderFolderContent(folder.id, 1)}
              </TaskTreeNode>
            ))}

            {/* Root projects (projects not in any folder) */}
            {rootProjects.map(project => (
              <TaskTreeNode
                key={project.id}
                id={project.id}
                type="project"
                name={project.name}
                level={0}
                color={project.color}
                isExpanded={expandedTreeNodes.has(project.id)}
                isSelected={getCurrentSelectedNode() === project.id}
                onToggle={() => toggleTreeNode(project.id)}
                onSelect={() => setSelectedTreeNode(project.id)}
                onContextAction={handleContextAction}
                isDragging={draggedItem?.id === project.id}
                isDragOver={dragOverItem === project.id}
                dropPosition={dragOverItem === project.id ? dropPosition : null}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onDragEnd={handleDragEnd}
              >
                {expandedTreeNodes.has(project.id) && getTasksInProject(project.id).map(task => (
                  <TaskTreeNode
                    key={task.id}
                    id={task.id}
                    type="task"
                    name={task.title}
                    level={1}
                    status={task.status}
                    isSelected={getCurrentSelectedNode() === task.id}
                    onSelect={() => setCurrentSelectedNode(task.id)}
                    onDoubleClick={() => {
                      setEditingTask(task);
                      setShowTaskForm(true);
                    }}
                    isDragging={draggedItem?.id === task.id}
                    isDragOver={dragOverItem === task.id}
                    dropPosition={dragOverItem === task.id ? dropPosition : null}
                    onDragStart={handleDragStart}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onDragEnd={handleDragEnd}
                  />
                ))}
              </TaskTreeNode>
            ))}
            </div>
          </div>
        </div>
      </div>

      {/* Forms */}
      {showTaskForm && (
        <TaskForm
          initialData={editingTask || undefined}
          onSubmit={async (taskData) => {
            try {
              if (editingTask) {
                // Update existing task
                const { comments, history, durations, subtasks, ...updateData } = taskData;
                await updateTask(editingTask.id, updateData);
              } else {
                // Create new task
                if (contextMenuTarget) {
                  taskData.projectId = contextMenuTarget;
                }
                await addTask(taskData);
              }

              // Note: syncData() removed to prevent conflicts - local state updates should be sufficient

              setShowTaskForm(false);
              setContextMenuTarget(null);
              setEditingTask(null);
            } catch (error) {
              console.error('Failed to save task:', error);
              alert(`Failed to save task: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
            }
          }}
          onClose={() => {
            setShowTaskForm(false);
            setContextMenuTarget(null);
            setEditingTask(null);
          }}
        />
      )}

      {showProjectForm && (
        <ProjectForm
          initialData={editingProject || undefined}
          onSubmit={async (projectData) => {
            try {
              if (editingProject) {
                // Update existing project
                await updateProject(editingProject.id, projectData);
              } else {
                // Create new project
                if (contextMenuTarget) {
                  projectData.folderId = contextMenuTarget;
                }
                await addProject(projectData);
              }

              // Note: syncData() removed to prevent conflicts - local state updates should be sufficient

              setShowProjectForm(false);
              setContextMenuTarget(null);
              setEditingProject(null);
            } catch (error) {
              console.error('Failed to save project:', error);
              alert('Failed to save project. Please try again.');
            }
          }}
          onClose={() => {
            setShowProjectForm(false);
            setContextMenuTarget(null);
            setEditingProject(null);
          }}
        />
      )}

      {showFolderForm && (
        <FolderForm
          initialData={editingFolder || undefined}
          onSubmit={async (folderData) => {
            try {
              if (editingFolder) {
                // Update existing folder
                await updateFolder(editingFolder.id, folderData);
              } else {
                // Create new folder
                if (contextMenuTarget) {
                  folderData.parentId = contextMenuTarget;
                }
                await addFolder(folderData);
              }

              // Note: syncData() removed to prevent conflicts - local state updates should be sufficient

              setShowFolderForm(false);
              setContextMenuTarget(null);
              setEditingFolder(null);
            } catch (error) {
              console.error('Failed to save folder:', error);
              alert('Failed to save folder. Please try again.');
            }
          }}
          onClose={() => {
            setShowFolderForm(false);
            setContextMenuTarget(null);
            setEditingFolder(null);
          }}
        />
      )}
    </>
  );
}








