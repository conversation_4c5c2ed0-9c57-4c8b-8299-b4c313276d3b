import React, { useState } from 'react';
import { Filter, X, ChevronDown, ChevronUp, FolderTree, Users, User, Zap, Settings } from 'lucide-react';
import { ColumnFilterConfig } from '../types/kanbanViews';
import { useSupabaseStore } from '../store/useSupabaseStore';
import FolderTreeSelector from './FolderTreeSelector';
import CustomFieldFilter from './CustomFieldFilter';
import EffortFilter from './EffortFilter';

interface ColumnFiltersSubmenuProps {
  filterConfig: ColumnFilterConfig;
  onFilterChange: (config: ColumnFilterConfig) => void;
  onClose: () => void;
}

export default function ColumnFiltersSubmenu({
  filterConfig,
  onFilterChange,
  onClose
}: ColumnFiltersSubmenuProps) {
  const { users, customFields } = useSupabaseStore();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    folders: false,
    customFields: false,
    assignees: false,
    owners: false,
    effort: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterToggle = () => {
    onFilterChange({
      ...filterConfig,
      enabled: !filterConfig.enabled
    });
  };

  const handleAssigneeChange = (userId: string, checked: boolean) => {
    const newAssigneeFilter = checked
      ? [...filterConfig.assigneeFilter, userId]
      : filterConfig.assigneeFilter.filter(id => id !== userId);
    
    onFilterChange({
      ...filterConfig,
      assigneeFilter: newAssigneeFilter
    });
  };

  const handleOwnerChange = (userId: string, checked: boolean) => {
    const newOwnerFilter = checked
      ? [...filterConfig.ownerFilter, userId]
      : filterConfig.ownerFilter.filter(id => id !== userId);
    
    onFilterChange({
      ...filterConfig,
      ownerFilter: newOwnerFilter
    });
  };

  const clearAllFilters = () => {
    onFilterChange({
      customFieldFilters: [],
      assigneeFilter: [],
      ownerFilter: [],
      enabled: false
    });
  };

  const hasActiveFilters = filterConfig.enabled && (
    (filterConfig.folderFilter?.selectedFolders.length ?? 0) > 0 ||
    filterConfig.customFieldFilters.length > 0 ||
    filterConfig.assigneeFilter.length > 0 ||
    filterConfig.ownerFilter.length > 0 ||
    filterConfig.effortFilter !== undefined
  );

  return (
    <div className="absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="font-medium text-gray-700">Column Filters</span>
            {hasActiveFilters && (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                Active
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
            title="Close filters"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Enable/Disable Toggle */}
        <div className="mt-3 flex items-center justify-between">
          <span className="text-sm text-gray-600">Enable column filtering</span>
          <button
            onClick={handleFilterToggle}
            className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
              filterConfig.enabled ? 'bg-blue-600' : 'bg-gray-300'
            }`}
          >
            <span
              className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                filterConfig.enabled ? 'translate-x-5' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Filter Sections */}
      {filterConfig.enabled && (
        <div className="max-h-96 overflow-y-auto">
          {/* Folder Filter */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('folders')}
              className="w-full flex items-center justify-between p-3 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-2">
                <FolderTree className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Location/Folder</span>
                {(filterConfig.folderFilter?.selectedFolders.length ?? 0) > 0 && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    {filterConfig.folderFilter?.selectedFolders.length}
                  </span>
                )}
              </div>
              {expandedSections.folders ? (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              )}
            </button>
            
            {expandedSections.folders && (
              <div className="px-3 pb-3">
                <FolderTreeSelector
                  selectedFolders={filterConfig.folderFilter?.selectedFolders ?? []}
                  includeSubfolders={filterConfig.folderFilter?.includeSubfolders ?? true}
                  onSelectionChange={(selectedFolders, includeSubfolders) => {
                    onFilterChange({
                      ...filterConfig,
                      folderFilter: { selectedFolders, includeSubfolders }
                    });
                  }}
                />
              </div>
            )}
          </div>

          {/* Custom Fields Filter */}
          {customFields && customFields.length > 0 && (
            <div className="border-b border-gray-100">
              <button
                onClick={() => toggleSection('customFields')}
                className="w-full flex items-center justify-between p-3 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <Settings className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Custom Fields</span>
                  {filterConfig.customFieldFilters.length > 0 && (
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      {filterConfig.customFieldFilters.length}
                    </span>
                  )}
                </div>
                {expandedSections.customFields ? (
                  <ChevronUp className="w-4 h-4 text-gray-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                )}
              </button>
              
              {expandedSections.customFields && (
                <div className="px-3 pb-3">
                  <CustomFieldFilter
                    customFields={customFields}
                    filters={filterConfig.customFieldFilters}
                    onFiltersChange={(filters) => {
                      onFilterChange({
                        ...filterConfig,
                        customFieldFilters: filters
                      });
                    }}
                  />
                </div>
              )}
            </div>
          )}

          {/* Assignee Filter */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('assignees')}
              className="w-full flex items-center justify-between p-3 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Assignees</span>
                {filterConfig.assigneeFilter.length > 0 && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    {filterConfig.assigneeFilter.length}
                  </span>
                )}
              </div>
              {expandedSections.assignees ? (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              )}
            </button>
            
            {expandedSections.assignees && (
              <div className="px-3 pb-3 max-h-32 overflow-y-auto">
                {users.map((user) => (
                  <label key={user.id} className="flex items-center gap-2 py-1">
                    <input
                      type="checkbox"
                      checked={filterConfig.assigneeFilter.includes(user.id)}
                      onChange={(e) => handleAssigneeChange(user.id, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{user.name || user.email}</span>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Owner Filter */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('owners')}
              className="w-full flex items-center justify-between p-3 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Owners</span>
                {filterConfig.ownerFilter.length > 0 && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    {filterConfig.ownerFilter.length}
                  </span>
                )}
              </div>
              {expandedSections.owners ? (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              )}
            </button>
            
            {expandedSections.owners && (
              <div className="px-3 pb-3 max-h-32 overflow-y-auto">
                {users.map((user) => (
                  <label key={user.id} className="flex items-center gap-2 py-1">
                    <input
                      type="checkbox"
                      checked={filterConfig.ownerFilter.includes(user.id)}
                      onChange={(e) => handleOwnerChange(user.id, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{user.name || user.email}</span>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Effort Filter */}
          <div>
            <button
              onClick={() => toggleSection('effort')}
              className="w-full flex items-center justify-between p-3 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Estimated Effort</span>
                {filterConfig.effortFilter && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    Active
                  </span>
                )}
              </div>
              {expandedSections.effort ? (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              )}
            </button>
            
            {expandedSections.effort && (
              <div className="px-3 pb-3">
                <EffortFilter
                  filter={filterConfig.effortFilter}
                  onFilterChange={(effortFilter) => {
                    onFilterChange({
                      ...filterConfig,
                      effortFilter
                    });
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      {filterConfig.enabled && hasActiveFilters && (
        <div className="p-3 border-t border-gray-200">
          <button
            onClick={clearAllFilters}
            className="w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded transition-colors"
          >
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  );
}
