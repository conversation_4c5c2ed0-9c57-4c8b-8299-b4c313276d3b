import { Task } from '../types';

/**
 * Check if a task is overdue
 * A task is considered overdue if:
 * - It has a due date
 * - The due date is before today
 * - The task is not completed (status !== 'done')
 */
export function isTaskOverdue(task: Task): boolean {
  if (!task.dueDate || task.status === 'done') {
    return false;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0); // Start of today
  
  const dueDate = new Date(task.dueDate);
  dueDate.setHours(0, 0, 0, 0); // Start of due date
  
  return dueDate < today;
}

/**
 * Get the appropriate CSS classes for a task title based on its overdue status
 */
export function getTaskTitleClasses(task: Task, baseClasses: string = 'font-medium'): string {
  if (isTaskOverdue(task)) {
    return `${baseClasses} text-red-600`;
  }
  return baseClasses;
}

/**
 * Check if a task is due today
 */
export function isTaskDueToday(task: Task): boolean {
  if (!task.dueDate) {
    return false;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dueDate = new Date(task.dueDate);
  dueDate.setHours(0, 0, 0, 0);
  
  return dueDate.getTime() === today.getTime();
}

/**
 * Check if a task is due within the next N days
 */
export function isTaskDueSoon(task: Task, days: number = 7): boolean {
  if (!task.dueDate || task.status === 'done') {
    return false;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const futureDate = new Date(today);
  futureDate.setDate(today.getDate() + days);
  
  const dueDate = new Date(task.dueDate);
  dueDate.setHours(0, 0, 0, 0);
  
  return dueDate >= today && dueDate <= futureDate;
}
