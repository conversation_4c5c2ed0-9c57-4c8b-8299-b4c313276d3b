-- Recurring Tasks Database Schema
-- This file contains all database changes needed for the recurring tasks functionality
--
-- FEATURES:
-- - Flexible recurrence pattern storage with JSONB configuration
-- - Comprehensive audit trail for all operations
-- - Execution tracking and statistics for admin reporting
-- - Optimized indexes for performance and admin queries
-- - Future-ready for admin audit dashboard
--
-- USAGE:
-- Run this file in your Supabase SQL editor after setting up the main schema

-- Create enum for recurrence status
-- active: recurrence is running normally
-- paused: recurrence is temporarily stopped
-- completed: recurrence has finished (reached end date or max executions)
-- cancelled: recurrence was deleted/cancelled by user
-- error: recurrence has encountered errors and stopped
CREATE TYPE recurrence_status AS ENUM ('active', 'paused', 'completed', 'cancelled', 'error');

-- Create enum for recurrence frequency types
CREATE TYPE recurrence_frequency AS ENUM ('daily', 'weekly', 'monthly', 'yearly', 'custom');

-- Main table for storing recurring task configurations
CREATE TABLE task_recurrences (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  
  -- Core recurrence configuration
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL, -- User-friendly name for the recurrence
  description TEXT,
  
  -- Recurrence pattern configuration (stored as JSONB for flexibility)
  frequency recurrence_frequency NOT NULL,
  pattern_config JSONB NOT NULL, -- Flexible pattern storage
  
  -- Scheduling information
  start_date DATE NOT NULL,
  end_date DATE, -- Optional end date
  next_execution_date TIMESTAMPTZ,
  timezone TEXT DEFAULT 'UTC',
  
  -- Status and control
  status recurrence_status DEFAULT 'active',
  is_active BOOLEAN DEFAULT true,
  max_executions INTEGER, -- Optional limit on total executions
  
  -- Execution statistics (for admin reporting)
  total_executions INTEGER DEFAULT 0,
  successful_executions INTEGER DEFAULT 0,
  failed_executions INTEGER DEFAULT 0,
  last_execution_date TIMESTAMPTZ,
  last_execution_status TEXT,
  
  -- Audit trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Soft delete support (following existing archive pattern)
  archived_at TIMESTAMPTZ,
  archived_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Version control for optimistic locking
  version INTEGER DEFAULT 1
);

-- Table for tracking individual executions (for detailed audit and reporting)
CREATE TABLE task_recurrence_executions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  
  -- Reference to the recurrence configuration
  recurrence_id UUID REFERENCES task_recurrences(id) ON DELETE CASCADE NOT NULL,
  
  -- Execution details
  scheduled_date TIMESTAMPTZ NOT NULL,
  executed_date TIMESTAMPTZ,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, success, failed, skipped
  
  -- Created task information
  created_task_id UUID REFERENCES tasks(id) ON DELETE SET NULL,
  
  -- Error handling
  error_message TEXT,
  error_details JSONB,
  
  -- Performance tracking
  execution_duration_ms INTEGER,
  
  -- Audit information
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance optimization

-- Primary lookup indexes
CREATE INDEX idx_task_recurrences_task_id ON task_recurrences(task_id);
CREATE INDEX idx_task_recurrences_status ON task_recurrences(status) WHERE archived_at IS NULL;
CREATE INDEX idx_task_recurrences_active ON task_recurrences(is_active, status) WHERE archived_at IS NULL;
CREATE INDEX idx_task_recurrences_next_execution ON task_recurrences(next_execution_date) WHERE is_active = true AND archived_at IS NULL;

-- Admin reporting indexes
CREATE INDEX idx_task_recurrences_created_by ON task_recurrences(created_by);
CREATE INDEX idx_task_recurrences_frequency ON task_recurrences(frequency);
CREATE INDEX idx_task_recurrences_created_at ON task_recurrences(created_at);

-- Execution tracking indexes
CREATE INDEX idx_task_recurrence_executions_recurrence_id ON task_recurrence_executions(recurrence_id);
CREATE INDEX idx_task_recurrence_executions_status ON task_recurrence_executions(status);
CREATE INDEX idx_task_recurrence_executions_scheduled_date ON task_recurrence_executions(scheduled_date);
CREATE INDEX idx_task_recurrence_executions_created_task_id ON task_recurrence_executions(created_task_id);

-- JSONB indexes for pattern configuration queries
CREATE INDEX idx_task_recurrences_pattern_config ON task_recurrences USING GIN (pattern_config);

-- Composite indexes for common admin queries
CREATE INDEX idx_task_recurrences_admin_overview ON task_recurrences(status, frequency, created_at) WHERE archived_at IS NULL;
CREATE INDEX idx_task_recurrence_executions_admin_stats ON task_recurrence_executions(recurrence_id, status, executed_date);

-- Functions for recurrence management

-- Function to update next execution date
CREATE OR REPLACE FUNCTION update_next_execution_date(recurrence_uuid UUID)
RETURNS TIMESTAMPTZ AS $$
DECLARE
    rec_record task_recurrences%ROWTYPE;
    next_date TIMESTAMPTZ;
BEGIN
    -- Get the recurrence record
    SELECT * INTO rec_record FROM task_recurrences WHERE id = recurrence_uuid;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Recurrence not found: %', recurrence_uuid;
    END IF;
    
    -- Calculate next execution date based on frequency and pattern
    -- This is a simplified version - the actual implementation will be in the service layer
    CASE rec_record.frequency
        WHEN 'daily' THEN
            next_date := COALESCE(rec_record.last_execution_date, rec_record.start_date::TIMESTAMPTZ) + INTERVAL '1 day';
        WHEN 'weekly' THEN
            next_date := COALESCE(rec_record.last_execution_date, rec_record.start_date::TIMESTAMPTZ) + INTERVAL '1 week';
        WHEN 'monthly' THEN
            next_date := COALESCE(rec_record.last_execution_date, rec_record.start_date::TIMESTAMPTZ) + INTERVAL '1 month';
        WHEN 'yearly' THEN
            next_date := COALESCE(rec_record.last_execution_date, rec_record.start_date::TIMESTAMPTZ) + INTERVAL '1 year';
        ELSE
            -- For custom patterns, we'll calculate in the service layer
            next_date := rec_record.next_execution_date;
    END CASE;
    
    -- Update the record
    UPDATE task_recurrences 
    SET next_execution_date = next_date, updated_at = NOW()
    WHERE id = recurrence_uuid;
    
    RETURN next_date;
END;
$$ LANGUAGE plpgsql;

-- Function to record execution
CREATE OR REPLACE FUNCTION record_recurrence_execution(
    recurrence_uuid UUID,
    execution_status TEXT,
    created_task_uuid UUID DEFAULT NULL,
    error_msg TEXT DEFAULT NULL,
    duration_ms INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    execution_id UUID;
BEGIN
    -- Insert execution record
    INSERT INTO task_recurrence_executions (
        recurrence_id,
        scheduled_date,
        executed_date,
        status,
        created_task_id,
        error_message,
        execution_duration_ms
    ) VALUES (
        recurrence_uuid,
        NOW(),
        NOW(),
        execution_status,
        created_task_uuid,
        error_msg,
        duration_ms
    ) RETURNING id INTO execution_id;
    
    -- Update recurrence statistics
    UPDATE task_recurrences SET
        total_executions = total_executions + 1,
        successful_executions = CASE WHEN execution_status = 'success' THEN successful_executions + 1 ELSE successful_executions END,
        failed_executions = CASE WHEN execution_status = 'failed' THEN failed_executions + 1 ELSE failed_executions END,
        last_execution_date = NOW(),
        last_execution_status = execution_status,
        updated_at = NOW()
    WHERE id = recurrence_uuid;
    
    RETURN execution_id;
END;
$$ LANGUAGE plpgsql;

-- Function for admin reporting - get recurrence statistics
CREATE OR REPLACE FUNCTION get_recurrence_statistics()
RETURNS TABLE (
    total_active_recurrences BIGINT,
    total_executions_today BIGINT,
    total_failed_executions_today BIGINT,
    avg_execution_duration_ms NUMERIC,
    most_frequent_frequency TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM task_recurrences WHERE is_active = true AND archived_at IS NULL) as total_active_recurrences,
        (SELECT COUNT(*) FROM task_recurrence_executions WHERE executed_date::DATE = CURRENT_DATE) as total_executions_today,
        (SELECT COUNT(*) FROM task_recurrence_executions WHERE executed_date::DATE = CURRENT_DATE AND status = 'failed') as total_failed_executions_today,
        (SELECT AVG(execution_duration_ms) FROM task_recurrence_executions WHERE executed_date >= CURRENT_DATE - INTERVAL '7 days') as avg_execution_duration_ms,
        (SELECT frequency::TEXT FROM task_recurrences WHERE archived_at IS NULL GROUP BY frequency ORDER BY COUNT(*) DESC LIMIT 1) as most_frequent_frequency;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_task_recurrences_updated_at
    BEFORE UPDATE ON task_recurrences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_recurrence_executions_updated_at
    BEFORE UPDATE ON task_recurrence_executions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies

-- Enable RLS on both tables
ALTER TABLE task_recurrences ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_recurrence_executions ENABLE ROW LEVEL SECURITY;

-- Policy for task_recurrences: users can see/modify recurrences for tasks they can access
CREATE POLICY "Users can view recurrences for visible tasks" ON task_recurrences
    FOR SELECT USING (
        task_id IN (SELECT id FROM tasks)
    );

CREATE POLICY "Users can create recurrences for editable tasks" ON task_recurrences
    FOR INSERT WITH CHECK (
        task_id IN (
            SELECT id FROM tasks WHERE
            created_by = auth.uid() OR
            assigned_user_id = auth.uid() OR
            owner_id = auth.uid() OR
            auth.uid()::text = ANY(assigned_users) OR
            EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
        )
    );

CREATE POLICY "Users can update recurrences for editable tasks" ON task_recurrences
    FOR UPDATE USING (
        task_id IN (
            SELECT id FROM tasks WHERE
            created_by = auth.uid() OR
            assigned_user_id = auth.uid() OR
            owner_id = auth.uid() OR
            auth.uid()::text = ANY(assigned_users) OR
            EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
        )
    );

CREATE POLICY "Users can delete recurrences for editable tasks" ON task_recurrences
    FOR DELETE USING (
        task_id IN (
            SELECT id FROM tasks WHERE
            created_by = auth.uid() OR
            assigned_user_id = auth.uid() OR
            owner_id = auth.uid() OR
            auth.uid()::text = ANY(assigned_users) OR
            EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
        )
    );

-- Policy for task_recurrence_executions: users can view/create executions for accessible recurrences
CREATE POLICY "Users can view executions for accessible recurrences" ON task_recurrence_executions
    FOR SELECT USING (
        recurrence_id IN (
            SELECT id FROM task_recurrences WHERE
            created_by = auth.uid() OR
            task_id IN (
                SELECT id FROM tasks WHERE
                created_by = auth.uid() OR
                assigned_user_id = auth.uid() OR
                owner_id = auth.uid() OR
                auth.uid()::text = ANY(assigned_users) OR
                EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
            )
        )
    );

CREATE POLICY "Users can create executions for accessible recurrences" ON task_recurrence_executions
    FOR INSERT WITH CHECK (
        recurrence_id IN (
            SELECT id FROM task_recurrences WHERE
            created_by = auth.uid() OR
            task_id IN (
                SELECT id FROM tasks WHERE
                created_by = auth.uid() OR
                assigned_user_id = auth.uid() OR
                owner_id = auth.uid() OR
                auth.uid()::text = ANY(assigned_users) OR
                EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
            )
        )
    );

-- Admin policies (for future admin dashboard)
-- Note: These will need to be activated when admin roles are implemented
-- CREATE POLICY "Admins can view all recurrences" ON task_recurrences
--     FOR ALL USING (
--         EXISTS (
--             SELECT 1 FROM user_profiles 
--             WHERE id = auth.uid() AND role = 'admin'
--         )
--     );
