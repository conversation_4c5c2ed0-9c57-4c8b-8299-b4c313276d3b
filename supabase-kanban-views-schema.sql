-- Custom Kanban Views Schema
-- This schema supports custom kanban views with extensible filter and column configuration
-- Designed to accommodate future filter enhancements and column-level sorting capabilities

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Custom kanban views table
CREATE TABLE kanban_views (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_default BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Extensible filter configuration (JSONB for future filter types)
  filter_config JSONB DEFAULT '{}',
  
  -- Column configuration with future sorting capabilities
  column_config JSONB DEFAULT '{}',
  
  -- View-level settings (compact mode, etc.)
  view_settings JSONB DEFAULT '{}',
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_used_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  
  -- Versioning for optimistic locking
  version INTEGER DEFAULT 1,
  updated_by UUID REFERENCES auth.users(id)
);

-- Kanban view sharing table
CREATE TABLE kanban_view_shares (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  view_id UUID REFERENCES kanban_views(id) ON DELETE CASCADE NOT NULL,
  shared_with_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  shared_by_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Permission levels for future enhancement
  permission_level TEXT DEFAULT 'read' CHECK (permission_level IN ('read', 'write', 'admin')),
  
  -- Sharing metadata
  shared_at TIMESTAMPTZ DEFAULT NOW(),
  last_accessed_at TIMESTAMPTZ,
  access_count INTEGER DEFAULT 0,
  
  -- Prevent duplicate shares
  UNIQUE(view_id, shared_with_user_id)
);

-- Indexes for performance
CREATE INDEX idx_kanban_views_created_by ON kanban_views(created_by);
CREATE INDEX idx_kanban_views_is_default ON kanban_views(is_default) WHERE is_default = TRUE;
CREATE INDEX idx_kanban_views_last_used ON kanban_views(last_used_at DESC);
CREATE INDEX idx_kanban_views_usage_count ON kanban_views(usage_count DESC);

CREATE INDEX idx_kanban_view_shares_view_id ON kanban_view_shares(view_id);
CREATE INDEX idx_kanban_view_shares_shared_with ON kanban_view_shares(shared_with_user_id);
CREATE INDEX idx_kanban_view_shares_shared_by ON kanban_view_shares(shared_by_user_id);

-- GIN indexes for JSONB columns to support future complex queries
CREATE INDEX idx_kanban_views_filter_config ON kanban_views USING GIN (filter_config);
CREATE INDEX idx_kanban_views_column_config ON kanban_views USING GIN (column_config);
CREATE INDEX idx_kanban_views_view_settings ON kanban_views USING GIN (view_settings);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_kanban_views_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.version = OLD.version + 1;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update timestamps
CREATE TRIGGER trigger_update_kanban_views_updated_at
  BEFORE UPDATE ON kanban_views
  FOR EACH ROW
  EXECUTE FUNCTION update_kanban_views_updated_at();

-- Function to update view usage statistics
CREATE OR REPLACE FUNCTION update_view_usage_stats(view_id UUID, user_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Update the view's usage statistics
  UPDATE kanban_views 
  SET 
    last_used_at = NOW(),
    usage_count = usage_count + 1
  WHERE id = view_id;
  
  -- Update share access statistics if this is a shared view
  UPDATE kanban_view_shares 
  SET 
    last_accessed_at = NOW(),
    access_count = access_count + 1
  WHERE view_id = view_id AND shared_with_user_id = user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create default view for new users
CREATE OR REPLACE FUNCTION create_default_kanban_view_for_user(user_id UUID)
RETURNS UUID AS $$
DECLARE
  default_view_id UUID;
  default_filter_config JSONB;
  default_column_config JSONB;
  default_view_settings JSONB;
BEGIN
  -- Default filter configuration (extensible structure)
  default_filter_config := jsonb_build_object(
    'selectedProject', 'all',
    'selectedUsers', '[]'::jsonb,
    'selectedGroups', '[]'::jsonb,
    'selectedOwners', '[]'::jsonb,
    'dueDateFilter', 'all',
    'customFilters', '{}'::jsonb,  -- For future filter types
    'advancedFilters', '{}'::jsonb -- For future advanced filtering
  );
  
  -- Default column configuration (extensible for future sorting)
  default_column_config := jsonb_build_object(
    'columns', jsonb_build_array(
      jsonb_build_object('id', 'todo', 'title', 'To Do', 'color', 'bg-gray-100', 'visible', true, 'sortConfig', '{}'::jsonb),
      jsonb_build_object('id', 'in-progress', 'title', 'In Progress', 'color', 'bg-blue-100', 'visible', true, 'sortConfig', '{}'::jsonb),
      jsonb_build_object('id', 'review', 'title', 'Review', 'color', 'bg-yellow-100', 'visible', true, 'sortConfig', '{}'::jsonb),
      jsonb_build_object('id', 'done', 'title', 'Done', 'color', 'bg-green-100', 'visible', true, 'sortConfig', '{}'::jsonb)
    ),
    'columnOrder', jsonb_build_array('todo', 'in-progress', 'review', 'done'),
    'globalSortConfig', '{}'::jsonb -- For future global sorting options
  );
  
  -- Default view settings
  default_view_settings := jsonb_build_object(
    'isCompactView', false,
    'showTaskCount', true,
    'showPriorityLabels', true,
    'highlightOverdue', true,
    'cardDisplayOptions', jsonb_build_object(
      'showAssignee', true,
      'showDueDate', true,
      'showPriority', true,
      'showTags', true
    ),
    'futureSettings', '{}'::jsonb -- For future view customizations
  );
  
  -- Create the default view
  INSERT INTO kanban_views (
    name,
    description,
    is_default,
    created_by,
    filter_config,
    column_config,
    view_settings
  ) VALUES (
    'Default View',
    'Standard kanban view with default settings',
    TRUE,
    user_id,
    default_filter_config,
    default_column_config,
    default_view_settings
  ) RETURNING id INTO default_view_id;
  
  RETURN default_view_id;
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security
ALTER TABLE kanban_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE kanban_view_shares ENABLE ROW LEVEL SECURITY;

-- RLS Policies for kanban_views
CREATE POLICY "Users can view their own views" ON kanban_views
  FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can view shared views" ON kanban_views
  FOR SELECT USING (
    id IN (
      SELECT view_id FROM kanban_view_shares 
      WHERE shared_with_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create their own views" ON kanban_views
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own views" ON kanban_views
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Users can delete their own views" ON kanban_views
  FOR DELETE USING (created_by = auth.uid());

-- RLS Policies for kanban_view_shares
CREATE POLICY "Users can view shares of their views" ON kanban_view_shares
  FOR SELECT USING (
    shared_by_user_id = auth.uid() OR shared_with_user_id = auth.uid()
  );

CREATE POLICY "Users can create shares for their views" ON kanban_view_shares
  FOR INSERT WITH CHECK (
    shared_by_user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM kanban_views 
      WHERE id = view_id AND created_by = auth.uid()
    )
  );

CREATE POLICY "Users can delete shares of their views" ON kanban_view_shares
  FOR DELETE USING (
    shared_by_user_id = auth.uid() OR shared_with_user_id = auth.uid()
  );

-- Comments for documentation
COMMENT ON TABLE kanban_views IS 'Custom kanban view configurations with extensible filter and column settings';
COMMENT ON TABLE kanban_view_shares IS 'Sharing permissions for kanban views between users';
COMMENT ON COLUMN kanban_views.filter_config IS 'Extensible JSONB configuration for current and future filter types';
COMMENT ON COLUMN kanban_views.column_config IS 'Extensible JSONB configuration for columns with future sorting capabilities';
COMMENT ON COLUMN kanban_views.view_settings IS 'General view settings and future customization options';
