import React, { useState } from 'react';
import { X, Save, Eye } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';

interface SaveViewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSaved?: () => void;
  currentState?: {
    selectedProject: string;
    selectedUsers: string[];
    selectedGroups: string[];
    selectedOwners: string[];
    dueDateFilter: 'all' | 'overdue' | 'today' | 'week' | 'month';
    isCompactView: boolean;
    columnConfigs?: Record<string, any>;
    columnOrder?: string[];
  };
}

export default function SaveViewDialog({ isOpen, onClose, onSaved, currentState }: SaveViewDialogProps) {
  const { saveCurrentStateAsView } = useSupabaseStore();
  
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('View name is required');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const view = await saveCurrentStateAsView(
        formData.name.trim(),
        formData.description.trim() || undefined,
        currentState
      );

      if (view) {
        // Reset form
        setFormData({ name: '', description: '' });
        onSaved?.();
        onClose();
      } else {
        setError('Failed to save view. Please try again.');
      }
    } catch (error) {
      console.error('Failed to save view:', error);
      setError(error.message || 'Failed to save view. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({ name: '', description: '' });
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Save Current View</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* View Name */}
            <div>
              <label htmlFor="viewName" className="block text-sm font-medium text-gray-700 mb-1">
                View Name *
              </label>
              <input
                id="viewName"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter view name..."
                disabled={isLoading}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-50"
                maxLength={100}
                required
              />
              <div className="text-xs text-gray-500 mt-1">
                {formData.name.length}/100 characters
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="viewDescription" className="block text-sm font-medium text-gray-700 mb-1">
                Description (Optional)
              </label>
              <textarea
                id="viewDescription"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this view is for..."
                disabled={isLoading}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-50 resize-none"
                maxLength={500}
              />
              <div className="text-xs text-gray-500 mt-1">
                {formData.description.length}/500 characters
              </div>
            </div>

            {/* Current View Preview */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">What will be saved:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Current filter settings</li>
                <li>• Column arrangement and visibility</li>
                <li>• View preferences (compact mode, etc.)</li>
                <li>• Card display options</li>
              </ul>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.name.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save View
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
