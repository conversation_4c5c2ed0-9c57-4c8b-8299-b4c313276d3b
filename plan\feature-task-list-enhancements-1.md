---
goal: Enhance Task List View with Horizontal Scroll and Recursive Folder Task Filtering
version: 1.0
date_created: 2025-09-12
last_updated: 2025-09-12
owner: Development Team
tags: [feature, ui-enhancement, task-management, folder-navigation]
---

# Introduction

This plan addresses two critical usability issues in the task list view: (1) insufficient width for deeply nested folder structures causing text truncation, and (2) limited task visibility when selecting folders that only shows direct tasks instead of all tasks in the folder hierarchy.

## 1. Requirements & Constraints

- **REQ-001**: Add horizontal scrollbar to tree section for deep nesting scenarios
- **REQ-002**: Implement recursive task filtering to show all tasks beneath selected folder
- **REQ-003**: Maintain existing UI/UX patterns and component structure
- **REQ-004**: Preserve current tree expansion/collapse functionality
- **REQ-005**: Ensure performance optimization for large folder hierarchies
- **SEC-001**: Maintain existing data access patterns and security boundaries
- **CON-001**: Must not break existing TaskTreeSidebar component functionality
- **CON-002**: Changes must be backward compatible with current folder/project structure
- **GUD-001**: Follow existing CSS class naming conventions
- **GUD-002**: Maintain consistent styling with current dark theme
- **PAT-001**: Use existing utility functions and store patterns where possible

## 2. Implementation Steps

### Phase 1: Horizontal Scroll Enhancement
1. **TASK-001**: Modify TaskTreeSidebar.tsx to add horizontal scroll container
   - Add `overflow-x-auto` class to tree content container
   - Implement `min-width` constraints to prevent excessive compression
   - Test with deeply nested folder structures

2. **TASK-002**: Update TaskTreeNode.tsx styling for horizontal scroll compatibility
   - Ensure proper width calculations for nested levels
   - Add `whitespace-nowrap` for text content to prevent wrapping
   - Maintain proper indentation with `paddingLeft` calculations

### Phase 2: Recursive Task Filtering
3. **TASK-003**: Create utility function for recursive folder traversal
   - Implement `getAllSubfolderIds(folderId: string, folders: Folder[]): string[]`
   - Add recursive logic to collect all descendant folder IDs
   - Include performance optimization for large hierarchies

4. **TASK-004**: Update TaskListView.tsx filtering logic
   - Modify `getFilteredTasks()` function to use recursive folder collection
   - Include tasks from all subfolders when folder is selected
   - Maintain existing project and direct task filtering

5. **TASK-005**: Update TaskFilterPanel.tsx with same recursive logic
   - Ensure filter panel uses same recursive task collection
   - Maintain consistency between list view and filter panel

6. **TASK-006**: Update KanbanBoard.tsx filtering logic
   - Apply same recursive folder filtering to kanban view
   - Ensure consistent behavior across all task views

## 3. Alternatives

- **ALT-001**: Virtual scrolling for tree - Rejected due to complexity and current tree size limitations
- **ALT-002**: Collapsible tree sections - Rejected as it doesn't solve the core width issue
- **ALT-003**: Separate folder selection mode - Rejected as it changes existing UX patterns
- **ALT-004**: Tooltip-based text display - Rejected as it doesn't provide persistent visibility

## 4. Dependencies

- **DEP-001**: Existing TaskTreeSidebar component structure
- **DEP-002**: Current folder/project data models in types/index.ts
- **DEP-003**: Zustand store implementation for task filtering
- **DEP-004**: Tailwind CSS classes for styling

## 5. Files

- **FILE-001**: src/components/TaskTreeSidebar.tsx - Add horizontal scroll container
- **FILE-002**: src/components/TaskTreeNode.tsx - Update styling for scroll compatibility
- **FILE-003**: src/components/TaskListView.tsx - Implement recursive task filtering
- **FILE-004**: src/components/TaskFilterPanel.tsx - Update filter logic
- **FILE-005**: src/components/KanbanBoard.tsx - Update kanban filtering
- **FILE-006**: src/utils/folderUtils.ts - New utility for recursive folder operations

## 6. Testing

- **TEST-001**: Create deeply nested folder structure (5+ levels) and verify horizontal scroll
- **TEST-002**: Test recursive task filtering with complex folder hierarchies
- **TEST-003**: Verify performance with large numbers of folders and tasks
- **TEST-004**: Test consistency across list, kanban, and filter panel views
- **TEST-005**: Verify existing functionality remains intact (expand/collapse, selection)

## 7. Risks & Assumptions

- **RISK-001**: Performance degradation with very large folder hierarchies
- **RISK-002**: Horizontal scroll may affect mobile responsiveness
- **RISK-003**: Recursive filtering might cause confusion for users expecting only direct tasks
- **ASSUMPTION-001**: Current folder structure supports unlimited nesting depth
- **ASSUMPTION-002**: Users prefer seeing all descendant tasks when selecting folders
- **ASSUMPTION-003**: Horizontal scroll is acceptable UX pattern for tree navigation

## 8. Related Specifications / Further Reading

- TaskTreeSidebar component documentation
- Zustand store patterns for task filtering
- Tailwind CSS overflow utilities documentation
