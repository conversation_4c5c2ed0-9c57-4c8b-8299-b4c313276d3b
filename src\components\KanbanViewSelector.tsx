import React, { useState, useEffect } from 'react';
import { ChevronDown, Plus, Settings, Share, Trash2, Edit3, Eye } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { KanbanView } from '../types/kanbanViews';

interface KanbanViewSelectorProps {
  onCreateView: () => void;
  onEditView: (view: KanbanView) => void;
  onShareView: (view: KanbanView) => void;
}

export default function KanbanViewSelector({ 
  onCreateView, 
  onEditView, 
  onShareView 
}: KanbanViewSelectorProps) {
  const {
    kanbanViews,
    currentKanbanView,
    viewSelectorState,
    setCurrentKanbanView,
    deleteKanbanView,
    loadKanbanViews
  } = useSupabaseStore();

  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load views on component mount
  useEffect(() => {
    const initializeViews = async () => {
      try {
        setIsLoading(true);
        await loadKanbanViews();
      } catch (error) {
        console.error('Failed to load kanban views:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeViews();
  }, [loadKanbanViews]);

  const handleViewSelect = async (viewId: string) => {
    try {
      setIsLoading(true);
      await setCurrentKanbanView(viewId);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to set current view:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteView = async (view: KanbanView, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (view.isDefault) {
      alert('Cannot delete the default view');
      return;
    }

    if (confirm(`Are you sure you want to delete the view "${view.name}"?`)) {
      try {
        setIsLoading(true);
        await deleteKanbanView(view.id);
      } catch (error) {
        console.error('Failed to delete view:', error);
        alert('Failed to delete view. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleEditView = (view: KanbanView, event: React.MouseEvent) => {
    event.stopPropagation();
    onEditView(view);
  };

  const handleShareView = (view: KanbanView, event: React.MouseEvent) => {
    event.stopPropagation();
    onShareView(view);
  };

  const allViews = [
    ...kanbanViews.ownedViews,
    ...kanbanViews.sharedViews
  ];

  const currentViewName = currentKanbanView?.name || 'Select View';
  const isCurrentViewDefault = currentKanbanView?.isDefault || false;

  return (
    <div className="relative">
      {/* View Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading || viewSelectorState.isLoading}
        className={`
          flex items-center gap-2 px-3 py-2 bg-white border rounded-lg text-sm font-medium
          transition-colors min-w-[160px] justify-between
          ${isLoading || viewSelectorState.isLoading 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:bg-gray-50 border-gray-200'
          }
        `}
      >
        <div className="flex items-center gap-2">
          <Eye className="w-4 h-4 text-gray-500" />
          <span className="truncate">{currentViewName}</span>
          {isCurrentViewDefault && (
            <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">
              Default
            </span>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-2">
            {/* Create New View Button */}
            <button
              onClick={() => {
                onCreateView();
                setIsOpen(false);
              }}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
            >
              <Plus className="w-4 h-4" />
              Create New View
            </button>

            <div className="border-t border-gray-100 my-2" />

            {/* Views List */}
            <div className="max-h-64 overflow-y-auto">
              {allViews.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500 text-center">
                  No views available
                </div>
              ) : (
                <>
                  {/* Owned Views */}
                  {kanbanViews.ownedViews.length > 0 && (
                    <>
                      <div className="px-3 py-1 text-xs font-medium text-gray-500 uppercase tracking-wide">
                        My Views
                      </div>
                      {kanbanViews.ownedViews.map((view) => (
                        <ViewItem
                          key={view.id}
                          view={view}
                          isSelected={currentKanbanView?.id === view.id}
                          onSelect={() => handleViewSelect(view.id)}
                          onEdit={(e) => handleEditView(view, e)}
                          onShare={(e) => handleShareView(view, e)}
                          onDelete={(e) => handleDeleteView(view, e)}
                          canEdit={true}
                          canDelete={!view.isDefault}
                        />
                      ))}
                    </>
                  )}

                  {/* Shared Views */}
                  {kanbanViews.sharedViews.length > 0 && (
                    <>
                      {kanbanViews.ownedViews.length > 0 && (
                        <div className="border-t border-gray-100 my-2" />
                      )}
                      <div className="px-3 py-1 text-xs font-medium text-gray-500 uppercase tracking-wide">
                        Shared with Me
                      </div>
                      {kanbanViews.sharedViews.map((view) => (
                        <ViewItem
                          key={view.id}
                          view={view}
                          isSelected={currentKanbanView?.id === view.id}
                          onSelect={() => handleViewSelect(view.id)}
                          onEdit={(e) => handleEditView(view, e)}
                          onShare={(e) => handleShareView(view, e)}
                          onDelete={(e) => handleDeleteView(view, e)}
                          canEdit={view.permissionLevel === 'write' || view.permissionLevel === 'admin'}
                          canDelete={false}
                          isShared={true}
                        />
                      ))}
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}

interface ViewItemProps {
  view: KanbanView;
  isSelected: boolean;
  onSelect: () => void;
  onEdit: (e: React.MouseEvent) => void;
  onShare: (e: React.MouseEvent) => void;
  onDelete: (e: React.MouseEvent) => void;
  canEdit: boolean;
  canDelete: boolean;
  isShared?: boolean;
}

function ViewItem({ 
  view, 
  isSelected, 
  onSelect, 
  onEdit, 
  onShare, 
  onDelete, 
  canEdit, 
  canDelete,
  isShared = false
}: ViewItemProps) {
  return (
    <div
      className={`
        flex items-center justify-between px-3 py-2 rounded-md cursor-pointer group
        ${isSelected 
          ? 'bg-blue-50 text-blue-700' 
          : 'text-gray-700 hover:bg-gray-100'
        }
      `}
      onClick={onSelect}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium truncate">{view.name}</span>
          {view.isDefault && (
            <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">
              Default
            </span>
          )}
          {isShared && (
            <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">
              Shared
            </span>
          )}
        </div>
        {view.description && (
          <p className="text-xs text-gray-500 truncate mt-0.5">
            {view.description}
          </p>
        )}
        <div className="text-xs text-gray-400 mt-0.5">
          Used {view.usageCount} times
        </div>
      </div>

      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        {canEdit && (
          <button
            onClick={onEdit}
            className="p-1 hover:bg-gray-200 rounded"
            title="Edit view"
          >
            <Edit3 className="w-3 h-3" />
          </button>
        )}
        <button
          onClick={onShare}
          className="p-1 hover:bg-gray-200 rounded"
          title="Share view"
        >
          <Share className="w-3 h-3" />
        </button>
        {canDelete && (
          <button
            onClick={onDelete}
            className="p-1 hover:bg-red-100 rounded text-red-600"
            title="Delete view"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        )}
      </div>
    </div>
  );
}
