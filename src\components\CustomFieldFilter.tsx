import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import { CustomField } from '../types';
import { 
  CustomFieldFilter as CustomFieldFilterType, 
  ComparisonOperator, 
  EmptyOperator, 
  TextOperator 
} from '../types/kanbanViews';

interface CustomFieldFilterProps {
  customFields: CustomField[];
  filters: CustomFieldFilterType[];
  onFiltersChange: (filters: CustomFieldFilterType[]) => void;
}

const EMPTY_OPERATORS: { value: EmptyOperator; label: string }[] = [
  { value: 'empty', label: 'Is empty' },
  { value: 'not_empty', label: 'Is not empty' }
];

const TEXT_OPERATORS: { value: TextOperator; label: string }[] = [
  { value: 'contains', label: 'Contains' },
  { value: 'not_contains', label: 'Does not contain' },
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Does not equal' }
];

const NUMERIC_OPERATORS: { value: ComparisonOperator; label: string }[] = [
  { value: 'equal', label: 'Equals' },
  { value: 'not_equal', label: 'Does not equal' },
  { value: 'greater_than', label: 'Greater than' },
  { value: 'less_than', label: 'Less than' },
  { value: 'greater_equal', label: 'Greater than or equal' },
  { value: 'less_equal', label: 'Less than or equal' }
];

const DATE_OPERATORS: { value: ComparisonOperator; label: string }[] = [
  { value: 'equal', label: 'On date' },
  { value: 'not_equal', label: 'Not on date' },
  { value: 'greater_than', label: 'After' },
  { value: 'less_than', label: 'Before' },
  { value: 'greater_equal', label: 'On or after' },
  { value: 'less_equal', label: 'On or before' }
];

export default function CustomFieldFilter({
  customFields,
  filters,
  onFiltersChange
}: CustomFieldFilterProps) {
  const [newFilter, setNewFilter] = useState<Partial<CustomFieldFilterType>>({});

  const getOperatorOptions = (fieldType: string) => {
    const baseOptions = [...EMPTY_OPERATORS];
    
    switch (fieldType) {
      case 'text':
      case 'dropdown':
        return [...baseOptions, ...TEXT_OPERATORS];
      case 'number':
        return [...baseOptions, ...NUMERIC_OPERATORS];
      case 'date':
        return [...baseOptions, ...DATE_OPERATORS];
      default:
        return baseOptions;
    }
  };

  const needsValue = (operator: string) => {
    return !['empty', 'not_empty'].includes(operator);
  };

  const addFilter = () => {
    if (!newFilter.fieldId || !newFilter.operator) {
      return;
    }

    const customField = customFields.find(f => f.id === newFilter.fieldId);
    if (!customField) {
      return;
    }

    // Validate value if needed
    if (needsValue(newFilter.operator) && !newFilter.value) {
      return;
    }

    const filter: CustomFieldFilterType = {
      fieldId: newFilter.fieldId,
      operator: newFilter.operator as any,
      value: needsValue(newFilter.operator) ? newFilter.value : undefined
    };

    onFiltersChange([...filters, filter]);
    setNewFilter({});
  };

  const removeFilter = (index: number) => {
    const newFilters = filters.filter((_, i) => i !== index);
    onFiltersChange(newFilters);
  };

  const updateFilter = (index: number, updates: Partial<CustomFieldFilterType>) => {
    const newFilters = filters.map((filter, i) => 
      i === index ? { ...filter, ...updates } : filter
    );
    onFiltersChange(newFilters);
  };

  const getFieldName = (fieldId: string) => {
    const field = customFields.find(f => f.id === fieldId);
    return field?.label || 'Unknown Field';
  };

  const getOperatorLabel = (operator: string, fieldType: string) => {
    const allOperators = getOperatorOptions(fieldType);
    const operatorOption = allOperators.find(op => op.value === operator);
    return operatorOption?.label || operator;
  };

  const renderValueInput = (
    value: string | number | undefined,
    fieldType: string,
    operator: string,
    onChange: (value: string | number) => void
  ) => {
    if (!needsValue(operator)) {
      return null;
    }

    const customField = customFields.find(f => f.id === newFilter.fieldId);

    switch (fieldType) {
      case 'number':
        return (
          <input
            type="number"
            value={value || ''}
            onChange={(e) => onChange(Number(e.target.value))}
            placeholder="Enter number"
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        );
      
      case 'date':
        return (
          <input
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        );
      
      case 'dropdown':
        return (
          <select
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select option</option>
            {customField?.dropdownOptions?.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      
      default:
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Enter text"
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        );
    }
  };

  return (
    <div className="space-y-3">
      {/* Existing Filters */}
      {filters.map((filter, index) => {
        const customField = customFields.find(f => f.id === filter.fieldId);
        if (!customField) return null;

        return (
          <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
            <div className="flex-1 space-y-1">
              <div className="text-xs font-medium text-gray-700">
                {getFieldName(filter.fieldId)}
              </div>
              <div className="text-xs text-gray-600">
                {getOperatorLabel(filter.operator, customField.fieldType)}
                {filter.value && `: ${filter.value}`}
              </div>
            </div>
            <button
              onClick={() => removeFilter(index)}
              className="p-1 hover:bg-gray-200 rounded"
              title="Remove filter"
            >
              <X className="w-3 h-3 text-gray-500" />
            </button>
          </div>
        );
      })}

      {/* Add New Filter */}
      <div className="space-y-2 p-2 border border-gray-200 rounded">
        <div className="text-xs font-medium text-gray-700">Add Custom Field Filter</div>
        
        {/* Field Selection */}
        <select
          value={newFilter.fieldId || ''}
          onChange={(e) => setNewFilter({ ...newFilter, fieldId: e.target.value, operator: undefined, value: undefined })}
          className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Select field</option>
          {customFields.filter(f => f.isActive).map((field) => (
            <option key={field.id} value={field.id}>
              {field.label}
            </option>
          ))}
        </select>

        {/* Operator Selection */}
        {newFilter.fieldId && (
          <select
            value={newFilter.operator || ''}
            onChange={(e) => setNewFilter({ ...newFilter, operator: e.target.value as any, value: undefined })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select condition</option>
            {getOperatorOptions(customFields.find(f => f.id === newFilter.fieldId)?.fieldType || 'text').map((op) => (
              <option key={op.value} value={op.value}>
                {op.label}
              </option>
            ))}
          </select>
        )}

        {/* Value Input */}
        {newFilter.fieldId && newFilter.operator && (
          <div>
            {renderValueInput(
              newFilter.value,
              customFields.find(f => f.id === newFilter.fieldId)?.fieldType || 'text',
              newFilter.operator,
              (value) => setNewFilter({ ...newFilter, value })
            )}
          </div>
        )}

        {/* Add Button */}
        <button
          onClick={addFilter}
          disabled={!newFilter.fieldId || !newFilter.operator || (needsValue(newFilter.operator || '') && !newFilter.value)}
          className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          <Plus className="w-3 h-3" />
          Add Filter
        </button>
      </div>

      {filters.length === 0 && (
        <div className="text-xs text-gray-500 text-center py-2">
          No custom field filters applied
        </div>
      )}
    </div>
  );
}
