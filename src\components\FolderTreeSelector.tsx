import React, { useState } from 'react';
import { ChevronRight, ChevronDown, Folder, FolderOpen } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { Folder as FolderType } from '../types';

interface FolderTreeSelectorProps {
  selectedFolders: string[];
  includeSubfolders: boolean;
  onSelectionChange: (selectedFolders: string[], includeSubfolders: boolean) => void;
}

interface FolderTreeItemProps {
  folder: FolderType;
  level: number;
  selectedFolders: string[];
  expandedFolders: Set<string>;
  onToggleExpand: (folderId: string) => void;
  onToggleSelect: (folderId: string) => void;
  children: FolderType[];
}

function FolderTreeItem({
  folder,
  level,
  selectedFolders,
  expandedFolders,
  onToggleExpand,
  onToggleSelect,
  children
}: FolderTreeItemProps) {
  const hasChildren = children.length > 0;
  const isExpanded = expandedFolders.has(folder.id);
  const isSelected = selectedFolders.includes(folder.id);

  return (
    <div>
      <div 
        className="flex items-center gap-1 py-1 hover:bg-gray-50 rounded"
        style={{ paddingLeft: `${level * 16}px` }}
      >
        {/* Expand/Collapse Button */}
        <button
          onClick={() => onToggleExpand(folder.id)}
          className="p-1 hover:bg-gray-100 rounded"
          disabled={!hasChildren}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="w-3 h-3 text-gray-400" />
            ) : (
              <ChevronRight className="w-3 h-3 text-gray-400" />
            )
          ) : (
            <div className="w-3 h-3" />
          )}
        </button>

        {/* Checkbox */}
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onToggleSelect(folder.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />

        {/* Folder Icon */}
        {isExpanded ? (
          <FolderOpen className="w-4 h-4 text-blue-500" />
        ) : (
          <Folder className="w-4 h-4 text-gray-500" />
        )}

        {/* Folder Name */}
        <span className="text-sm text-gray-700 flex-1 truncate">
          {folder.name}
        </span>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div>
          {children.map((childFolder) => (
            <FolderTreeItemContainer
              key={childFolder.id}
              folder={childFolder}
              level={level + 1}
              selectedFolders={selectedFolders}
              expandedFolders={expandedFolders}
              onToggleExpand={onToggleExpand}
              onToggleSelect={onToggleSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function FolderTreeItemContainer({
  folder,
  level,
  selectedFolders,
  expandedFolders,
  onToggleExpand,
  onToggleSelect
}: Omit<FolderTreeItemProps, 'children'>) {
  const { folders } = useSupabaseStore();
  const children = folders.filter(f => f.parentId === folder.id);

  return (
    <FolderTreeItem
      folder={folder}
      level={level}
      selectedFolders={selectedFolders}
      expandedFolders={expandedFolders}
      onToggleExpand={onToggleExpand}
      onToggleSelect={onToggleSelect}
      children={children}
    />
  );
}

export default function FolderTreeSelector({
  selectedFolders,
  includeSubfolders,
  onSelectionChange
}: FolderTreeSelectorProps) {
  const { folders } = useSupabaseStore();
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());

  // Get root folders (folders with no parent)
  const rootFolders = folders.filter(folder => !folder.parentId);

  const handleToggleExpand = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleToggleSelect = (folderId: string) => {
    const newSelected = selectedFolders.includes(folderId)
      ? selectedFolders.filter(id => id !== folderId)
      : [...selectedFolders, folderId];
    
    onSelectionChange(newSelected, includeSubfolders);
  };

  const handleIncludeSubfoldersChange = (checked: boolean) => {
    onSelectionChange(selectedFolders, checked);
  };

  const selectAll = () => {
    const allFolderIds = folders.map(f => f.id);
    onSelectionChange(allFolderIds, includeSubfolders);
  };

  const clearAll = () => {
    onSelectionChange([], includeSubfolders);
  };

  const expandAll = () => {
    const allFolderIds = new Set(folders.map(f => f.id));
    setExpandedFolders(allFolderIds);
  };

  const collapseAll = () => {
    setExpandedFolders(new Set());
  };

  return (
    <div className="space-y-3">
      {/* Controls */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex gap-2">
          <button
            onClick={selectAll}
            className="text-blue-600 hover:text-blue-800"
          >
            Select All
          </button>
          <button
            onClick={clearAll}
            className="text-gray-600 hover:text-gray-800"
          >
            Clear All
          </button>
        </div>
        <div className="flex gap-2">
          <button
            onClick={expandAll}
            className="text-gray-600 hover:text-gray-800"
          >
            Expand All
          </button>
          <button
            onClick={collapseAll}
            className="text-gray-600 hover:text-gray-800"
          >
            Collapse All
          </button>
        </div>
      </div>

      {/* Include Subfolders Option */}
      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
        <input
          type="checkbox"
          id="includeSubfolders"
          checked={includeSubfolders}
          onChange={(e) => handleIncludeSubfoldersChange(e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="includeSubfolders" className="text-sm text-gray-700">
          Include subfolders
        </label>
      </div>

      {/* Folder Tree */}
      <div className="max-h-48 overflow-y-auto border border-gray-200 rounded p-2">
        {rootFolders.length === 0 ? (
          <div className="text-sm text-gray-500 text-center py-4">
            No folders available
          </div>
        ) : (
          <div className="space-y-1">
            {rootFolders.map((folder) => (
              <FolderTreeItemContainer
                key={folder.id}
                folder={folder}
                level={0}
                selectedFolders={selectedFolders}
                expandedFolders={expandedFolders}
                onToggleExpand={handleToggleExpand}
                onToggleSelect={handleToggleSelect}
              />
            ))}
          </div>
        )}
      </div>

      {/* Selection Summary */}
      {selectedFolders.length > 0 && (
        <div className="text-xs text-gray-600">
          {selectedFolders.length} folder{selectedFolders.length !== 1 ? 's' : ''} selected
          {includeSubfolders && ' (including subfolders)'}
        </div>
      )}
    </div>
  );
}
