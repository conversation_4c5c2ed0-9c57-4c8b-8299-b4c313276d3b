-- CRITICAL FIX for Recurring Tasks Infinite Loop Issue
-- 
-- PROBLEM: Recurring tasks were creating hundreds of duplicate tasks due to:
-- 1. Missing RLS policies for task_recurrence_executions table
-- 2. Failed execution recording causing infinite retry loops
-- 3. Next execution date not being updated on failures
--
-- This fix addresses the RLS policy issues and provides emergency cleanup
--
-- ⚠️ EMERGENCY: Run this immediately if you have recurring tasks creating infinite duplicates

-- Step 1: Stop all active recurring tasks immediately
UPDATE task_recurrences
SET is_active = false, status = 'paused'
WHERE is_active = true;

-- Fix for deleted recurrences that have incorrect status
-- (This addresses an issue where deleted recurrences kept status = 'active')
UPDATE task_recurrences
SET status = 'cancelled'
WHERE archived_at IS NOT NULL AND status != 'cancelled';

-- Step 2: Add missing RLS policies for task_recurrence_executions table

-- Drop existing restrictive policy
DROP POLICY IF EXISTS "Users can view executions for their recurrences" ON task_recurrence_executions;

-- Create comprehensive SELECT policy
CREATE POLICY "Users can view executions for accessible recurrences" ON task_recurrence_executions
    FOR SELECT USING (
        recurrence_id IN (
            SELECT id FROM task_recurrences WHERE 
            created_by = auth.uid() OR 
            task_id IN (
                SELECT id FROM tasks WHERE 
                created_by = auth.uid() OR 
                assigned_user_id = auth.uid() OR 
                owner_id = auth.uid() OR 
                auth.uid()::text = ANY(assigned_users) OR 
                EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
            )
        )
    );

-- Create missing INSERT policy (this was the main cause of the issue)
CREATE POLICY "Users can create executions for accessible recurrences" ON task_recurrence_executions
    FOR INSERT WITH CHECK (
        recurrence_id IN (
            SELECT id FROM task_recurrences WHERE 
            created_by = auth.uid() OR 
            task_id IN (
                SELECT id FROM tasks WHERE 
                created_by = auth.uid() OR 
                assigned_user_id = auth.uid() OR 
                owner_id = auth.uid() OR 
                auth.uid()::text = ANY(assigned_users) OR 
                EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
            )
        )
    );

-- Step 3: Fix next execution dates for any recurrences that might be stuck in the past
UPDATE task_recurrences 
SET next_execution_date = (CURRENT_DATE + INTERVAL '1 day')::TIMESTAMPTZ + TIME '06:00:00'
WHERE next_execution_date < NOW() AND is_active = false;

-- Step 4: Clean up duplicate tasks created today (OPTIONAL - only if you have duplicates)
-- Uncomment the following lines if you need to clean up duplicate clone tasks:
-- 
-- DELETE FROM tasks 
-- WHERE title LIKE 'Clone_%' 
-- AND created_at::date = CURRENT_DATE 
-- AND title LIKE '%fsdf task 1%';

-- Step 5: Verification queries
-- Run these to verify the fix worked:

-- Check that policies are in place
SELECT policyname, cmd 
FROM pg_policies 
WHERE tablename = 'task_recurrence_executions' 
ORDER BY cmd;

-- Check recurrence states
SELECT id, name, is_active, status, next_execution_date 
FROM task_recurrences 
WHERE archived_at IS NULL;

-- Check for any tasks created today
SELECT COUNT(*) as tasks_created_today 
FROM tasks 
WHERE created_at::date = CURRENT_DATE;

-- Step 6: Re-enable recurrences (ONLY after verifying the fix)
-- Uncomment and run this ONLY after confirming everything is working:
--
-- UPDATE task_recurrences 
-- SET is_active = true, status = 'active' 
-- WHERE archived_at IS NULL 
-- AND end_date IS NULL OR end_date >= CURRENT_DATE;

-- IMPORTANT NOTES:
-- 1. This fix stops all recurring tasks immediately to prevent further damage
-- 2. You must manually re-enable recurring tasks after verifying the fix
-- 3. The missing INSERT policy was the root cause - execution recording failed
-- 4. When execution recording fails, the scheduler doesn't update next_execution_date
-- 5. This creates an infinite loop where the same recurrence is processed repeatedly
-- 6. Always test recurring tasks in a development environment first
