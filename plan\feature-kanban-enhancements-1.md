---
goal: Enhance Kanban View with Task Counts, Priority Labels, Overdue Highlighting, and Compact View Toggle
version: 1.0
date_created: 2025-01-13
last_updated: 2025-01-13
owner: Development Team
tags: [feature, ui-enhancement, kanban, user-experience]
---

# Introduction

This plan implements a comprehensive set of enhancements to the kanban view including task count display in column headers, priority labels on task cards, overdue task highlighting, removal of purple task IDs, and a compact view toggle for improved space utilization.

## 1. Requirements & Constraints

- **REQ-001**: Add task count display next to each column header showing number of tasks in that column
- **REQ-002**: Display priority labels on each task card using existing priority field
- **REQ-003**: Highlight overdue tasks with red font color for visual emphasis
- **REQ-004**: Remove purple task ID display from task cards (currently showing as purple text)
- **REQ-005**: Add compact view toggle that reduces card size and shows essential info in 1-2 rows
- **SEC-001**: Maintain existing security patterns and data access controls
- **CON-001**: Must not break existing kanban functionality or drag-and-drop behavior
- **CON-002**: Must maintain responsive design across different screen sizes
- **GUD-001**: Follow existing component patterns and styling conventions
- **GUD-002**: Use existing utility functions for date handling and priority display
- **PAT-001**: Implement compact view as a toggle state in component, not global store

## 2. Implementation Steps

### Phase 1: Column Header Enhancements
- Modify KanbanBoard.tsx column header section to include task counts
- Calculate filtered task count per column dynamically
- Style count display consistently with existing header design

### Phase 2: Task Card Priority Labels
- Add priority badge display to task cards using existing getPriorityBadge utility
- Position priority label prominently on each card
- Ensure priority labels work with existing card layout

### Phase 3: Overdue Task Highlighting
- Create utility function to detect overdue tasks
- Apply red font styling to overdue task titles
- Ensure highlighting works with existing card hover states

### Phase 4: Remove Purple Task ID
- Identify and remove purple task ID display from task cards
- Verify no functionality depends on visible task ID

### Phase 5: Compact View Toggle
- Add compact view state to KanbanBoard component
- Create toggle button in kanban header area
- Implement compact card layout showing title, assignee, due date, priority in 1-2 rows
- Hide description in compact mode
- Adjust card spacing and sizing for compact view

## 3. Alternatives

- **ALT-001**: Store compact view preference in global state - rejected to keep implementation simple and component-scoped
- **ALT-002**: Use different color schemes for overdue highlighting - rejected to maintain consistency with existing red color usage
- **ALT-003**: Show task counts in separate UI element - rejected in favor of inline display for better space utilization

## 4. Dependencies

- **DEP-001**: Existing getPriorityBadge utility function from src/utils/statusUtils.tsx
- **DEP-002**: Date handling utilities for overdue detection (date-fns library already available)
- **DEP-003**: Existing task filtering logic in KanbanBoard component
- **DEP-004**: Lucide React icons for compact view toggle button

## 5. Files

- **FILE-001**: src/components/KanbanBoard.tsx - Main kanban component requiring header and card modifications
- **FILE-002**: src/utils/statusUtils.tsx - May need overdue detection utility function
- **FILE-003**: src/utils/dateUtils.ts - New file for overdue task detection utilities

## 6. Testing

- **TEST-001**: Verify task counts update correctly when tasks are moved between columns
- **TEST-002**: Confirm priority labels display correctly for all priority levels (low, medium, high)
- **TEST-003**: Test overdue highlighting with various due date scenarios
- **TEST-004**: Validate compact view toggle preserves all essential task information
- **TEST-005**: Ensure drag-and-drop functionality works in both normal and compact views
- **TEST-006**: Test responsive behavior on different screen sizes

## 7. Risks & Assumptions

- **RISK-001**: Compact view might make task cards too small for effective interaction on mobile devices
- **RISK-002**: Adding more visual elements to cards could create visual clutter
- **ASSUMPTION-001**: Current task data structure includes all necessary fields (priority, dueDate)
- **ASSUMPTION-002**: Existing filtering logic will work correctly with new count calculations
- **ASSUMPTION-003**: Users will find compact view useful for high-density task management

## 8. Related Specifications / Further Reading

- Existing kanban implementation in src/components/KanbanBoard.tsx
- Priority badge utilities in src/utils/statusUtils.tsx
- Task type definitions in src/types/index.ts
- Date handling patterns in src/utils/capacityCalculations.ts
