# Custom Kanban Views Guide

This guide covers the custom kanban views functionality that allows users to create, save, and share personalized kanban board configurations.

## 🎯 Overview

Custom kanban views enable users to:
- Create personalized kanban board configurations
- Save custom filter combinations and column arrangements
- Share views with other users
- Switch between different view configurations
- Maintain a default view for standard usage

## 🚀 Features

### View Management
- **Create Custom Views**: Save current kanban state as a named view
- **Edit Views**: Modify existing view configurations
- **Delete Views**: Remove unwanted custom views
- **Default View**: Automatic default view creation for new users

### Sharing & Collaboration
- **Share Views**: Share custom views with specific users by email
- **Permission Levels**: Read, write, and admin permissions for shared views
- **Access Control**: Secure view access through Row Level Security

### Extensible Architecture
- **Future-Ready Filters**: Extensible filter configuration for upcoming enhancements
- **Column Sorting**: Prepared for future column-level sorting capabilities
- **Custom Settings**: Expandable view settings for new features

## 📋 Database Schema

### kanban_views Table
Stores custom kanban view configurations with extensible JSONB fields:

```sql
CREATE TABLE kanban_views (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_default BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  
  -- Extensible configurations
  filter_config JSONB DEFAULT '{}',
  column_config JSONB DEFAULT '{}',
  view_settings JSONB DEFAULT '{}',
  
  -- Metadata and versioning
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_used_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  version INTEGER DEFAULT 1
);
```

### kanban_view_shares Table
Manages view sharing between users:

```sql
CREATE TABLE kanban_view_shares (
  id UUID PRIMARY KEY,
  view_id UUID REFERENCES kanban_views(id),
  shared_with_user_id UUID REFERENCES auth.users(id),
  shared_by_user_id UUID REFERENCES auth.users(id),
  permission_level view_permission DEFAULT 'read',
  shared_at TIMESTAMPTZ DEFAULT NOW(),
  access_count INTEGER DEFAULT 0
);
```

## 🔧 Configuration Structure

### Filter Configuration
Extensible filter settings stored in JSONB:

```typescript
interface KanbanFilterConfig {
  selectedProject: string;
  selectedUsers: string[];
  selectedGroups: string[];
  selectedOwners: string[];
  dueDateFilter: 'all' | 'overdue' | 'today' | 'week' | 'month';
  
  // Extensible for future enhancements
  customFilters: Record<string, any>;
  advancedFilters: Record<string, any>;
}
```

### Column Configuration
Column settings with future sorting capabilities:

```typescript
interface KanbanColumnsConfig {
  columns: KanbanColumnConfig[];
  columnOrder: string[];
  globalSortConfig: {
    enabled?: boolean;
    defaultSort?: string;
    customGlobalSort?: Record<string, any>;
  };
}

interface KanbanColumnConfig {
  id: string;
  title: string;
  color: string;
  visible: boolean;
  position?: number;
  sortConfig: ColumnSortConfig; // For future sorting
}
```

### View Settings
General view customization options:

```typescript
interface KanbanViewSettings {
  isCompactView: boolean;
  showTaskCount: boolean;
  showPriorityLabels: boolean;
  highlightOverdue: boolean;
  cardDisplayOptions: CardDisplayOptions;
  futureSettings: Record<string, any>; // Extensible
}
```

## 🛠️ Implementation

### Service Layer
The `kanbanViewService` provides all view management operations:

- `getUserViews()` - Get all accessible views for current user
- `createView(input)` - Create new custom view
- `updateView(viewId, input)` - Update existing view
- `deleteView(viewId)` - Delete view
- `shareView(input)` - Share view with another user
- `updateViewUsage(viewId)` - Track view usage statistics

### Store Integration
Custom views are integrated into the Supabase store:

```typescript
interface SupabaseStoreState {
  kanbanViews: KanbanViewList;
  currentKanbanView: KanbanView | null;
  viewSelectorState: ViewSelectorState;
  
  // Actions
  loadKanbanViews: () => Promise<void>;
  createKanbanView: (input) => Promise<KanbanView | null>;
  setCurrentKanbanView: (viewId) => Promise<void>;
  // ... more actions
}
```

## 🔒 Security

### Row Level Security (RLS)
Comprehensive RLS policies ensure secure access:

- Users can only view their own views and views shared with them
- Only view creators can update/delete their views
- Sharing requires ownership of the view
- All operations are authenticated

### Permission Levels
- **Read**: View and use the shared view
- **Write**: Modify the shared view (future enhancement)
- **Admin**: Full control including sharing management (future enhancement)

## 🚀 Usage Examples

### Creating a Custom View
```typescript
const viewInput = {
  name: "My Sprint View",
  description: "Focused view for current sprint tasks",
  filterConfig: {
    selectedProject: "sprint-project-id",
    selectedUsers: ["user1", "user2"],
    dueDateFilter: "week"
  },
  columnConfig: {
    columns: [...customColumns],
    columnOrder: ["todo", "in-progress", "done"]
  },
  viewSettings: {
    isCompactView: true,
    showTaskCount: true
  }
};

const view = await store.createKanbanView(viewInput);
```

### Sharing a View
```typescript
const shareInput = {
  viewId: "view-uuid",
  userEmail: "<EMAIL>",
  permissionLevel: "read"
};

const success = await store.shareKanbanView(shareInput);
```

### Switching Views
```typescript
await store.setCurrentKanbanView("view-uuid");
// View configuration is automatically applied to kanban board
```

## 🔮 Future Enhancements

The extensible architecture supports upcoming features:

### Advanced Filtering
- Custom filter types in `customFilters` JSONB field
- Complex filter combinations in `advancedFilters`
- Dynamic filter UI generation

### Column-Level Sorting
- Sort configuration per column in `sortConfig`
- Global sorting options in `globalSortConfig`
- Custom sort fields support

### Enhanced Sharing
- Team-based view sharing
- View templates and categories
- Public view sharing options

## 📚 Related Documentation

- [Database Schema](./database-schema.md) - Complete database documentation
- [Supabase Setup Guide](../SUPABASE_SETUP.md) - Setup instructions
- [Multi-User Collaboration Guide](./multi-user-collaboration-guide.md) - Collaboration features
