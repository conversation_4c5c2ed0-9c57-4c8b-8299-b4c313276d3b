# Recurring Tasks Setup Guide

This document provides setup instructions for the recurring tasks functionality in the project management tool.

## Database Setup

### 1. Run the Recurring Tasks Schema

Execute the following SQL file in your Supabase SQL editor:

```sql
-- File: supabase-recurring-tasks-schema.sql
```

**Note**: If you installed recurring tasks before this fix and users can't see recurring task indicators for tasks created by others, also run:

```sql
-- File: fix-recurring-tasks-rls.sql
```

This creates:
- `task_recurrences` table for storing recurring task configurations
- `task_recurrence_executions` table for tracking execution history
- Database functions for managing recurrences and executions
- Proper indexes and RLS policies

### 2. Verify Database Functions

Ensure these functions are created:
- `record_recurrence_execution()` - Records execution attempts
- `update_next_execution_date()` - Calculates next execution dates
- `get_recurrence_statistics()` - Provides admin statistics
- `update_task_recurrence_updated_at()` - Trigger for version tracking

## Application Features

### 1. Setting Up Recurring Tasks

Users can set up recurring schedules through:
1. Open any task in TaskForm or SubtaskForm
2. Click the three-dot menu (⋮)
3. Select "Set Recurring Task"
4. Configure the recurrence pattern:
   - **Daily**: Every N days, with optional weekdays-only
   - **Weekly**: Every N weeks on specific days
   - **Monthly**: Every N months on specific dates or week patterns
   - **Yearly**: Every N years with month/date specifications

### 2. Visual Indicators

Tasks with active recurring schedules display:
- **Blue rotating arrow icon** (RotateCcw) next to the task title
- Visible in both Kanban Board and List View
- Tooltip shows "This task has a recurring schedule"

### 3. Automatic Execution

The system automatically:
- Checks for due recurrences every minute
- Clones tasks when their execution time arrives
- Updates next execution dates based on the pattern
- Records execution statistics and history
- Handles errors and failure recovery

## Technical Implementation

### 1. Core Services

- **RecurrenceService** (`src/services/recurrenceService.ts`)
  - CRUD operations for recurring task configurations
  - Pattern validation and date calculations
  - Execution recording and statistics

- **RecurrenceScheduler** (`src/services/recurrenceScheduler.ts`)
  - Background scheduler that runs every minute
  - Processes due recurrences automatically
  - Handles task cloning and error recovery
  - Auto-starts in both development and production

### 2. UI Components

- **RecurringTaskModal** (`src/components/RecurringTaskModal.tsx`)
  - Main configuration interface
  - Pattern selection and preview
  - Form validation and submission

- **RecurrencePatternSelector** (`src/components/RecurrencePatternSelector.tsx`)
  - Flexible pattern configuration UI
  - Supports all recurrence types

- **RecurrencePreview** (`src/components/RecurrencePreview.tsx`)
  - Shows next execution dates
  - Helps users verify their configuration

### 3. Data Flow

1. **Configuration**: User sets up recurrence through UI
2. **Storage**: Configuration saved to `task_recurrences` table
3. **Scheduling**: Scheduler checks for due recurrences every minute
4. **Execution**: Due tasks are cloned using existing clone service
5. **Recording**: Execution results recorded in `task_recurrence_executions`
6. **Update**: Next execution date calculated and updated

## Monitoring and Debugging

### 1. Database Queries

Check active recurrences:
```sql
SELECT * FROM task_recurrences WHERE is_active = true AND archived_at IS NULL;
```

Check recent executions:
```sql
SELECT * FROM task_recurrence_executions ORDER BY created_at DESC LIMIT 10;
```

Get statistics:
```sql
SELECT * FROM get_recurrence_statistics();
```

### 2. Browser Console

The scheduler logs its activity:
- "🔄 Processing recurring tasks..." - Scheduler is running
- "Found X recurrences due for execution" - Due tasks found
- "✅ Successfully executed recurrence..." - Successful execution
- "❌ Failed to execute recurrence..." - Execution errors

### 3. Manual Testing

Use the test page at `/test-recurring-tasks.html` to:
- Check scheduler status
- Manually trigger executions
- Verify database state

## Troubleshooting

### Common Issues

1. **Scheduler Not Running**
   - Check browser console for initialization messages
   - Verify `recurrenceScheduler.getStatus().isRunning` returns `true`

2. **Tasks Not Being Created**
   - Check if execution time has passed
   - Verify recurrence is active (`is_active = true`)
   - Check for errors in execution records

3. **Visual Indicators Missing**
   - Ensure task recurrences are loaded in the store
   - Check if `taskRecurrences` array contains the task's recurrence

4. **Execution Recording Issues**
   - Verify database functions exist and have proper permissions
   - Check RLS policies allow execution recording
   - Look for errors in browser console

### Performance Considerations

- Scheduler runs every minute (configurable via `CHECK_INTERVAL_MS`)
- Database queries are optimized with proper indexes
- Failed executions are tracked to prevent infinite retries
- Cleanup mechanisms prevent orphaned recurrences

## Future Enhancements

- Admin dashboard for monitoring all recurring tasks
- Bulk operations for managing multiple recurrences
- Advanced pattern types (business days, custom schedules)
- Email notifications for execution failures
- Export/import of recurrence configurations
