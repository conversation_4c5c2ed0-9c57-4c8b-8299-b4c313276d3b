import React, { useState } from 'react';
import { X, Share, Mail, Users, Check } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { KanbanView } from '../types/kanbanViews';

interface ShareViewDialogProps {
  isOpen: boolean;
  view: KanbanView | null;
  onClose: () => void;
  onShared?: () => void;
}

export default function ShareViewDialog({ isOpen, view, onClose, onShared }: ShareViewDialogProps) {
  const { shareKanbanView } = useSupabaseStore();
  
  const [formData, setFormData] = useState({
    userEmail: '',
    permissionLevel: 'read' as 'read' | 'write' | 'admin'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!view) return;
    
    if (!formData.userEmail.trim()) {
      setError('Email address is required');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.userEmail.trim())) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      const success = await shareKanbanView({
        viewId: view.id,
        userEmail: formData.userEmail.trim(),
        permissionLevel: formData.permissionLevel
      });

      if (success) {
        setSuccess(`View shared successfully with ${formData.userEmail}`);
        setFormData({ userEmail: '', permissionLevel: 'read' });
        onShared?.();
        
        // Auto-close after 2 seconds
        setTimeout(() => {
          handleClose();
        }, 2000);
      } else {
        setError('Failed to share view. Please check the email address and try again.');
      }
    } catch (error) {
      console.error('Failed to share view:', error);
      setError(error.message || 'Failed to share view. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({ userEmail: '', permissionLevel: 'read' });
      setError(null);
      setSuccess(null);
      onClose();
    }
  };

  if (!isOpen || !view) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Share className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Share View</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* View Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-1">Sharing View:</h4>
              <p className="text-sm font-semibold text-gray-900">{view.name}</p>
              {view.description && (
                <p className="text-sm text-gray-600 mt-1">{view.description}</p>
              )}
            </div>

            {/* User Email */}
            <div>
              <label htmlFor="userEmail" className="block text-sm font-medium text-gray-700 mb-1">
                User Email *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  id="userEmail"
                  type="email"
                  value={formData.userEmail}
                  onChange={(e) => setFormData(prev => ({ ...prev, userEmail: e.target.value }))}
                  placeholder="Enter user's email address..."
                  disabled={isLoading}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-50"
                  required
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                The user must have an account in the system
              </p>
            </div>

            {/* Permission Level */}
            <div>
              <label htmlFor="permissionLevel" className="block text-sm font-medium text-gray-700 mb-1">
                Permission Level
              </label>
              <select
                id="permissionLevel"
                value={formData.permissionLevel}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  permissionLevel: e.target.value as 'read' | 'write' | 'admin' 
                }))}
                disabled={isLoading}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-50"
              >
                <option value="read">Read Only - Can view and use the view</option>
                <option value="write">Write - Can modify the view (Future)</option>
                <option value="admin">Admin - Full control (Future)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Currently only "Read Only" permission is fully implemented
              </p>
            </div>

            {/* Success Message */}
            {success && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <p className="text-sm text-green-600">{success}</p>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Sharing Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Users className="w-4 h-4 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">How sharing works:</p>
                  <ul className="text-xs text-blue-700 mt-1 space-y-1">
                    <li>• The user will see this view in their "Shared with Me" section</li>
                    <li>• They can use the view but cannot modify your original</li>
                    <li>• You can revoke access at any time</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.userEmail.trim() || success !== null}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Sharing...
                </>
              ) : success ? (
                <>
                  <Check className="w-4 h-4" />
                  Shared!
                </>
              ) : (
                <>
                  <Share className="w-4 h-4" />
                  Share View
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
