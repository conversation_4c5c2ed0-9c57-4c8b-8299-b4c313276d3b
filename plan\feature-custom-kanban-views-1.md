---
goal: Implement Custom and Saved Kanban Views for Multi-User Environment
version: 1.0
date_created: 2025-01-14
last_updated: 2025-01-14
owner: Development Team
tags: [feature, kanban, views, multi-user, customization, sharing]
---

# Introduction

This plan implements custom and saved kanban views functionality that allows each logged-in user to create, save, and share personalized kanban board configurations. Users can customize filters, column arrangements, colors, and share views with specific users while maintaining the standard default view for all users.

## 1. Requirements & Constraints

- **REQ-001**: Each user must have access to a standard default kanban view upon login
- **REQ-002**: Users must be able to create custom views with personalized filters, column arrangements, and colors
- **REQ-003**: Custom views must be saveable with user-defined names
- **REQ-004**: Users must be able to share custom views with specific other users by username
- **REQ-005**: View selection must be available via compact dropdown above filters
- **REQ-006**: Users must be able to edit and delete their own custom views
- **REQ-007**: Shared views must be read-only for recipients unless explicitly granted edit permissions
- **REQ-008**: View system must be extensible for future filter enhancements and column-level sorting
- **REQ-009**: Filter configuration must support dynamic addition of new filter types
- **SEC-001**: Users can only access views they created or views shared with them
- **SEC-002**: View sharing must be controlled through user permissions
- **CON-001**: Implementation must not disrupt existing kanban functionality
- **CON-002**: UI changes must be minimal and compact to preserve screen space
- **CON-003**: Must follow existing database schema patterns and RLS policies
- **GUD-001**: Follow application-level validation over complex RLS policies
- **GUD-002**: Implement modular components that don't disturb existing codebase
- **PAT-001**: Use existing Supabase store patterns for state management
- **PAT-002**: Follow existing component structure and styling patterns
- **PAT-003**: Design extensible filter system architecture for future enhancements
- **PAT-004**: Implement column-level configuration structure for future sorting capabilities

## 2. Implementation Steps

### Phase 1: Database Schema and Types
- **TASK-001**: Create `kanban_views` table with user ownership and sharing capabilities
- **TASK-002**: Create `kanban_view_shares` table for view sharing permissions
- **TASK-003**: Add TypeScript interfaces for KanbanView and related types
- **TASK-004**: Create database service functions for view management
- **TASK-005**: Implement RLS policies for secure view access

### Phase 2: Core View Management
- **TASK-006**: Create KanbanViewManager component for view CRUD operations
- **TASK-007**: Implement view persistence and loading functionality
- **TASK-008**: Create view sharing functionality with user selection
- **TASK-009**: Add view validation and error handling
- **TASK-010**: Implement default view fallback mechanism

### Phase 3: UI Integration
- **TASK-011**: Create compact view selector dropdown component
- **TASK-012**: Add save current view functionality with naming dialog
- **TASK-013**: Integrate view selector above existing filters
- **TASK-014**: Implement view edit/delete options in dropdown
- **TASK-015**: Add view sharing dialog with user search

### Phase 4: Column Customization
- **TASK-016**: Implement custom column creation and editing
- **TASK-017**: Add column color customization functionality
- **TASK-018**: Implement column reordering and deletion
- **TASK-019**: Add column visibility toggle functionality
- **TASK-020**: Ensure column changes are saved to custom views

### Phase 5: Filter Integration
- **TASK-021**: Extend existing filter system to work with custom views
- **TASK-022**: Implement filter persistence in custom views
- **TASK-023**: Add advanced filter options for custom views
- **TASK-024**: Ensure filter state synchronization with view changes
- **TASK-025**: Implement filter reset functionality per view

## 3. Alternatives

- **ALT-001**: Store view configurations in local storage - Rejected due to multi-user sharing requirements
- **ALT-002**: Use single table with JSONB for all view data - Rejected for better normalization and query performance
- **ALT-003**: Implement view templates instead of full customization - Rejected as user requested full customization capabilities

## 4. Dependencies

- **DEP-001**: Existing Supabase database and authentication system
- **DEP-002**: Current KanbanBoard and KanbanFilters components
- **DEP-003**: useSupabaseStore for state management
- **DEP-004**: Existing user management and permissions system
- **DEP-005**: Current column management functionality

## 5. Files

- **FILE-001**: `supabase-kanban-views-schema.sql` - Database schema for custom views
- **FILE-002**: `src/types/kanbanViews.ts` - TypeScript interfaces for view types
- **FILE-003**: `src/services/kanbanViewService.ts` - Database service for view operations
- **FILE-004**: `src/components/KanbanViewSelector.tsx` - View selection dropdown component
- **FILE-005**: `src/components/KanbanViewManager.tsx` - View management functionality
- **FILE-006**: `src/components/SaveViewDialog.tsx` - Dialog for saving new views
- **FILE-007**: `src/components/ShareViewDialog.tsx` - Dialog for sharing views
- **FILE-008**: `src/components/KanbanBoard.tsx` - Updated to support custom views
- **FILE-009**: `src/store/useSupabaseStore.ts` - Extended with view management state
- **FILE-010**: `docs/custom-kanban-views-guide.md` - Documentation for the feature

## 6. Testing

- **TEST-001**: Verify default view loads correctly for new users
- **TEST-002**: Test custom view creation, saving, and loading
- **TEST-003**: Verify view sharing functionality between users
- **TEST-004**: Test view editing and deletion permissions
- **TEST-005**: Verify column customization persists correctly
- **TEST-006**: Test filter integration with custom views
- **TEST-007**: Verify RLS policies prevent unauthorized access
- **TEST-008**: Test view selector UI responsiveness and compactness

## 7. Risks & Assumptions

- **RISK-001**: Complex view configurations may impact performance - Mitigate with efficient queries and caching
- **RISK-002**: User confusion with multiple views - Mitigate with clear UI and documentation
- **RISK-003**: View sharing permissions complexity - Mitigate with simple sharing model
- **ASSUMPTION-001**: Users will primarily use 2-5 custom views each
- **ASSUMPTION-002**: View sharing will be used moderately, not extensively
- **ASSUMPTION-003**: Existing kanban performance is acceptable for custom views

## 8. Related Specifications / Further Reading

- [Database Schema Documentation](../docs/database-schema.md)
- [Kanban Board Implementation](../src/components/KanbanBoard.tsx)
- [Supabase Store Patterns](../src/store/useSupabaseStore.ts)
- [Multi-User Collaboration Guide](../docs/multi-user-collaboration-guide.md)
