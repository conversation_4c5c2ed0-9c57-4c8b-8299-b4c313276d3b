# Database Schema Documentation

This document describes the complete database schema for the Project Management Tool.

## 🏷️ Custom Types

### Enums
```sql
-- User roles
CREATE TYPE user_role AS ENUM ('admin', 'user');

-- Task statuses
CREATE TYPE task_status AS ENUM ('todo', 'in_progress', 'review', 'done');

-- Task priorities
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high', 'urgent');

-- Dependency types
CREATE TYPE dependency_type AS ENUM ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish');

-- Recurrence status
CREATE TYPE recurrence_status AS ENUM ('active', 'paused', 'completed', 'cancelled', 'error');

-- Recurrence frequency
CREATE TYPE recurrence_frequency AS ENUM ('daily', 'weekly', 'monthly', 'yearly', 'custom');

-- View permission levels
CREATE TYPE view_permission AS ENUM ('read', 'write', 'admin');
```

## 📊 Core Tables

### user_profiles
Extends Supabase auth.users with application-specific data.

```sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  avatar_url TEXT,
  role user_role DEFAULT 'user',
  group_id UUID REFERENCES user_groups(id),
  skillset_ids UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features**:
- Links to Supabase auth system
- Role-based access (admin/user)
- Group membership and skillset assignments
- Automatic profile creation via triggers

### tasks
Main task management with subtask support.

```sql
CREATE TABLE tasks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  status task_status DEFAULT 'todo',
  priority task_priority DEFAULT 'medium',
  assigned_user_id UUID REFERENCES auth.users(id),
  assigned_users TEXT[] DEFAULT '{}',
  assigned_groups TEXT[] DEFAULT '{}',
  owner_id UUID REFERENCES auth.users(id),
  due_date DATE,
  start_date DATE,
  tags TEXT[] DEFAULT '{}',
  project_id UUID REFERENCES projects(id),
  folder_id UUID REFERENCES folders(id),
  effort JSONB,
  subtasks JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

**Key Features**:
- Full task lifecycle management
- Multiple assignment options (user, groups)
- Hierarchical organization (projects, folders)
- JSONB subtasks for nested task structure
- Effort estimation and tracking

### task_dependencies
Task dependency relationships for project scheduling.

```sql
CREATE TABLE task_dependencies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  predecessor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  successor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  dependency_type dependency_type DEFAULT 'finish_to_start',
  lag_days INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  UNIQUE(predecessor_task_id, successor_task_id)
);
```

**Key Features**:
- Finish-to-start dependency relationships
- Automatic date calculation support
- Lag time configuration for delays
- Prevents circular dependencies via constraints
- Cascade deletion when tasks are removed

### projects
Project organization and management.

```sql
CREATE TABLE projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  color TEXT NOT NULL,
  start_date DATE,
  end_date DATE,
  folder_id UUID REFERENCES folders(id),
  effort JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

### folders
Hierarchical folder structure for organization.

```sql
CREATE TABLE folders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  parent_id UUID REFERENCES folders(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

## 👥 User Management Tables

### user_groups
Team organization and grouping.

```sql
CREATE TABLE user_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

### skillset_groups
Skills and competency management.

```sql
CREATE TABLE skillset_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

### user_capacities
Resource planning and availability tracking.

```sql
CREATE TABLE user_capacities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  daily_hours DECIMAL(4,2) NOT NULL,
  weekly_hours DECIMAL(4,2) NOT NULL,
  working_days INTEGER[] NOT NULL,
  effective_from DATE NOT NULL,
  effective_to DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 📝 Activity Tracking Tables

### task_comments
Threaded comments on tasks and subtasks with real-time updates.

```sql
CREATE TABLE task_comments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES task_comments(id),
  edited BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features**:
- Real-time comment synchronization across all users
- Optimistic updates for immediate user feedback
- Threaded comment support with parent-child relationships
- Edit tracking with timestamp updates
- Automatic duplicate prevention

### task_history
Change tracking and audit trail with user information.

```sql
CREATE TABLE task_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  field TEXT NOT NULL,
  old_value TEXT NOT NULL,
  new_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features**:
- Real-time history updates across all users
- User information display (avatars and names) in history entries
- Comprehensive change tracking for all task fields
- Automatic value normalization to prevent spurious entries
- Smart duplicate prevention for null/empty value changes

### task_durations
Time tracking for tasks.

```sql
CREATE TABLE task_durations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  status TEXT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🎯 Planning Tables

### task_efforts
Effort estimation and resource planning.

```sql
CREATE TABLE task_efforts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  estimated_hours DECIMAL(6,2) NOT NULL,
  actual_hours DECIMAL(6,2),
  assigned_user_id UUID REFERENCES auth.users(id),
  required_skillsets UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### kanban_columns
Customizable workflow columns.

```sql
CREATE TABLE kanban_columns (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  color TEXT NOT NULL,
  position INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

## 🔒 Security Features

### Row Level Security (RLS)
All tables have RLS enabled with policies for:
- **Users**: Can view all data, update own profiles
- **Admins**: Can manage all data and users
- **Creators**: Can manage their own created content

### Key Policies
```sql
-- User profile access
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can update any profile" ON user_profiles FOR UPDATE USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Task management
CREATE POLICY "Users can view all tasks" ON tasks FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create tasks" ON tasks FOR INSERT WITH CHECK (auth.role() = 'authenticated');
```

## 🔧 Database Functions & Triggers

### User Creation Trigger
Automatically creates user profiles when users sign up:

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    CASE WHEN NEW.email = '<EMAIL>' THEN 'admin'::user_role ELSE 'user'::user_role END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### kanban_views
Custom kanban view configurations with extensible filter and column settings.

```sql
CREATE TABLE kanban_views (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_default BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

  -- Extensible filter configuration (JSONB for future filter types)
  filter_config JSONB DEFAULT '{}',

  -- Column configuration with future sorting capabilities
  column_config JSONB DEFAULT '{}',

  -- View-level settings (compact mode, etc.)
  view_settings JSONB DEFAULT '{}',

  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_used_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,

  -- Versioning for optimistic locking
  version INTEGER DEFAULT 1,
  updated_by UUID REFERENCES auth.users(id)
);
```

### kanban_view_shares
Sharing permissions for kanban views between users.

```sql
CREATE TABLE kanban_view_shares (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  view_id UUID REFERENCES kanban_views(id) ON DELETE CASCADE NOT NULL,
  shared_with_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  shared_by_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

  -- Permission levels for future enhancement
  permission_level view_permission DEFAULT 'read',

  -- Sharing metadata
  shared_at TIMESTAMPTZ DEFAULT NOW(),
  last_accessed_at TIMESTAMPTZ,
  access_count INTEGER DEFAULT 0,

  -- Prevent duplicate shares
  UNIQUE(view_id, shared_with_user_id)
);
```

### Update Timestamp Triggers
Automatically update `updated_at` columns on all tables.

## 📈 Indexes

Key indexes for performance:
- `idx_tasks_assigned_user_id` - Task assignment queries
- `idx_tasks_project_id` - Project-based filtering
- `idx_tasks_status` - Status-based queries
- `idx_task_comments_task_id` - Comment retrieval
- `idx_user_capacities_user_id` - Capacity lookups
- `idx_tasks_subtasks` - JSONB subtask queries (GIN index)
- `idx_kanban_views_created_by` - User's kanban views
- `idx_kanban_views_filter_config` - GIN index for filter queries
- `idx_kanban_views_column_config` - GIN index for column queries
- `idx_kanban_view_shares_view_id` - View sharing lookups
- `idx_kanban_view_shares_shared_with` - User's shared views

## 🔄 Data Relationships

```
auth.users (Supabase)
├── user_profiles (1:1)
├── user_capacities (1:many)
├── tasks (created_by, assigned_user_id)
├── projects (created_by)
└── folders (created_by)

user_groups
├── user_profiles (group_id)
└── tasks (assigned_groups)

projects
├── tasks (project_id)
└── folders (folder_id)

tasks
├── task_comments (task_id)
├── task_history (task_id)
├── task_durations (task_id)
├── task_efforts (task_id)
└── subtasks (JSONB array)

kanban_views
├── kanban_view_shares (view_id)
└── created_by (auth.users)
```

## 🗄️ Archive System

### Archive Columns
All main tables (`folders`, `projects`, `tasks`) include archive functionality:

```sql
-- Archive columns added to folders, projects, and tasks
ALTER TABLE folders ADD COLUMN archived_at TIMESTAMPTZ;
ALTER TABLE folders ADD COLUMN archived_by UUID REFERENCES auth.users(id);
ALTER TABLE folders ADD COLUMN original_parent_id UUID;

ALTER TABLE projects ADD COLUMN archived_at TIMESTAMPTZ;
ALTER TABLE projects ADD COLUMN archived_by UUID REFERENCES auth.users(id);
ALTER TABLE projects ADD COLUMN original_folder_id UUID;

ALTER TABLE tasks ADD COLUMN archived_at TIMESTAMPTZ;
ALTER TABLE tasks ADD COLUMN archived_by UUID REFERENCES auth.users(id);
ALTER TABLE tasks ADD COLUMN original_project_id UUID;
ALTER TABLE tasks ADD COLUMN original_folder_id UUID;
```

### Archive Functions

#### archive_folder_cascade(folder_id, user_id)
Recursively archives a folder and all its contents:
- Archives the folder itself
- Archives all subfolders recursively
- Archives all projects in the folder (using `archive_project_cascade`)
- Archives all tasks directly in the folder

#### archive_project_cascade(project_id, user_id)
Archives a project and all its tasks:
- Archives the project itself
- Archives all tasks in the project
- Preserves original folder location for restoration

#### restore_folder_cascade(folder_id)
Restores a folder and all its contents:
- Restores the folder to its original location
- Restores all subfolders recursively
- Restores all projects in the folder
- Restores all tasks directly in the folder

#### restore_project_cascade(project_id)
Restores a project and all its tasks:
- Restores the project to its original location
- Restores all tasks in the project

#### cleanup_old_archives()
Automatically removes archived items older than 1 week:
- Permanently deletes folders, projects, and tasks archived more than 7 days ago
- Called automatically via scheduled function

### Archive Features
- **Soft Delete**: Items are marked as archived, not permanently deleted
- **Cascade Operations**: Archiving a folder archives all its contents
- **Original Location Tracking**: Stores original parent/folder/project IDs for restoration
- **Admin-Only Access**: Only admin users can view and restore archived items
- **Automatic Cleanup**: Items older than 1 week are permanently deleted
- **Filtered Queries**: Normal operations exclude archived items automatically

## 🔁 Recurring Tasks System

### task_recurrences
Stores recurring task configurations with flexible pattern support.

```sql
CREATE TABLE task_recurrences (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  frequency recurrence_frequency NOT NULL,
  pattern_config JSONB NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  next_execution_date TIMESTAMPTZ,
  timezone TEXT DEFAULT 'UTC',
  status recurrence_status DEFAULT 'active',
  is_active BOOLEAN DEFAULT true,
  max_executions INTEGER,
  total_executions INTEGER DEFAULT 0,
  successful_executions INTEGER DEFAULT 0,
  failed_executions INTEGER DEFAULT 0,
  last_execution_date TIMESTAMPTZ,
  last_execution_status TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  archived_at TIMESTAMPTZ,
  archived_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  version INTEGER DEFAULT 1
);
```

**Key Features**:
- Flexible JSONB pattern configuration for complex recurrence rules
- Support for daily, weekly, monthly, yearly, and custom patterns
- Execution statistics and audit trail
- Soft delete support with archive functionality
- Version control for optimistic locking

### task_recurrence_executions
Tracks individual execution attempts for audit and reporting.

```sql
CREATE TABLE task_recurrence_executions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  recurrence_id UUID REFERENCES task_recurrences(id) ON DELETE CASCADE NOT NULL,
  scheduled_date TIMESTAMPTZ NOT NULL,
  executed_date TIMESTAMPTZ,
  status TEXT NOT NULL DEFAULT 'pending',
  created_task_id UUID REFERENCES tasks(id) ON DELETE SET NULL,
  error_message TEXT,
  error_details JSONB,
  execution_duration_ms INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features**:
- Complete execution audit trail
- Performance tracking with duration metrics
- Error logging with detailed JSONB error information
- Links to created tasks for full traceability

### Recurring Tasks Functions

#### record_recurrence_execution(recurrence_uuid, execution_status, created_task_uuid, error_msg, duration_ms)
Records execution attempts and updates statistics:
- Creates execution record in `task_recurrence_executions`
- Updates execution counters in `task_recurrences`
- Tracks success/failure rates for reporting

#### update_next_execution_date(recurrence_uuid)
Calculates and updates the next execution date:
- Supports all frequency types (daily, weekly, monthly, yearly)
- Handles complex patterns through service layer integration
- Updates `next_execution_date` field automatically

#### get_recurrence_statistics()
Provides admin reporting data:
- Total active recurrences count
- Daily execution statistics
- Average execution duration
- Most frequent recurrence patterns

### Recurring Tasks Features
- **Visual Indicators**: Blue rotating arrow icons for tasks with active recurrences
- **Context-Aware UI**: "Set Recurring Task" vs "Edit Recurring Schedule" menu options
- **Automatic Execution**: Background scheduler processes due recurrences every minute
- **Pattern Flexibility**: JSONB configuration supports complex recurrence rules
- **Audit Trail**: Complete tracking of all execution attempts and outcomes
- **Performance Monitoring**: Execution duration and success rate tracking

## 🔄 Real-time Functionality

### Supabase Real-time Publication
All critical tables are included in the `supabase_realtime` publication for live updates:

```sql
-- Tables included in real-time publication
ALTER PUBLICATION supabase_realtime ADD TABLE tasks;
ALTER PUBLICATION supabase_realtime ADD TABLE projects;
ALTER PUBLICATION supabase_realtime ADD TABLE folders;
ALTER PUBLICATION supabase_realtime ADD TABLE kanban_columns;
ALTER PUBLICATION supabase_realtime ADD TABLE task_comments;
ALTER PUBLICATION supabase_realtime ADD TABLE task_history;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE user_profiles;
ALTER PUBLICATION supabase_realtime ADD TABLE user_groups;
ALTER PUBLICATION supabase_realtime ADD TABLE skillset_groups;
ALTER PUBLICATION supabase_realtime ADD TABLE user_capacities;
ALTER PUBLICATION supabase_realtime ADD TABLE task_recurrences;
ALTER PUBLICATION supabase_realtime ADD TABLE task_recurrence_executions;
```

### Real-time Features
- **Task Updates**: Live synchronization of task changes across all users
- **Comment System**: Optimistic updates with real-time confirmation
- **History Tracking**: Immediate history entry updates with user information
- **Notifications**: Real-time notification delivery and read status updates
- **User Management**: Live updates for user profiles and group changes
- **Recurring Tasks**: Live updates to recurring task configurations and execution status

### Optimistic Updates
The application implements optimistic updates for better user experience:

#### Comments
- **Immediate Display**: Comments appear instantly with "sending..." indicator
- **Real-time Confirmation**: Optimistic comments replaced with real data
- **Error Handling**: Automatic rollback on failure
- **Duplicate Prevention**: Smart matching prevents duplicate entries

#### Task History
- **Live Updates**: History entries appear immediately without page refresh
- **User Information**: Shows user avatars and names for each change
- **Value Normalization**: Prevents spurious entries from null/empty value changes

### Connection Management
- **Subscription Monitoring**: Tracks 10 real-time channels for complete coverage
- **Fallback Polling**: Automatic fallback when real-time connections fail
- **Connection Recovery**: Automatic reconnection and data synchronization

This schema supports full project management functionality with proper security, performance, data integrity, comprehensive archive management, and real-time collaboration features.
