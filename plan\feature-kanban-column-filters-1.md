---
goal: Add Column-Specific Filtering Options to Kanban View Dropdown Menus
version: 1.0
date_created: 2025-01-28
last_updated: 2025-01-28
owner: Development Team
tags: [feature, kanban, filtering, ui-enhancement]
---

# Introduction

This plan implements column-specific filtering options in the kanban view dropdown menus. Each column will have its own filters submenu allowing users to filter tasks by folder/location hierarchy, custom fields, assignee, owner, and estimated effort. This provides granular control over what tasks appear in each column while maintaining the existing global filtering system.

## 1. Requirements & Constraints

- **REQ-001**: Add filters submenu to existing column dropdown menus
- **REQ-002**: Support folder/location filtering with hierarchical tree traversal
- **REQ-003**: Support custom field filtering (empty, not empty, specific value, not specific value)
- **REQ-004**: Support assignee, owner, and estimated effort filtering
- **REQ-005**: Maintain existing global filtering functionality
- **REQ-006**: Preserve current column configuration and sorting features
- **REQ-007**: Ensure filters are applied per column independently
- **REQ-008**: Support effort filtering (null/empty, not empty, more than, less than, equal to)

- **SEC-001**: Ensure user can only filter by data they have access to
- **SEC-002**: Validate all filter inputs to prevent injection attacks

- **CON-001**: Must not break existing kanban functionality
- **CON-002**: Must follow existing UI patterns and design system
- **CON-003**: Must be performant with large task datasets
- **CON-004**: Must integrate with existing custom fields system

- **GUD-001**: Follow modular component architecture
- **GUD-002**: Use TypeScript for type safety
- **GUD-003**: Implement proper error handling and loading states

- **PAT-001**: Follow existing filter component patterns from KanbanFilters.tsx
- **PAT-002**: Use existing dropdown menu patterns from ColumnHeaderMenu.tsx
- **PAT-003**: Follow folder hierarchy patterns from folderUtils.ts

## 2. Implementation Steps

### Phase 1: Type Definitions and Data Structures
1. **TASK-001**: Extend ColumnConfig interface to include column-specific filters
2. **TASK-002**: Create ColumnFilterConfig interface for filter state management
3. **TASK-003**: Define filter option types for custom fields, effort, etc.

### Phase 2: Core Filter Logic
4. **TASK-004**: Create column filter utility functions for task filtering
5. **TASK-005**: Implement folder hierarchy traversal for location filtering
6. **TASK-006**: Create custom field filter logic with value comparison
7. **TASK-007**: Implement effort estimation filtering logic

### Phase 3: UI Components
8. **TASK-008**: Create ColumnFiltersSubmenu component
9. **TASK-009**: Create FolderTreeSelector component for location filtering
10. **TASK-010**: Create CustomFieldFilter component for field-based filtering
11. **TASK-011**: Create EffortFilter component for effort-based filtering

### Phase 4: Integration
12. **TASK-012**: Integrate filters submenu into ColumnHeaderMenu
13. **TASK-013**: Update KanbanBoard to apply column-specific filters
14. **TASK-014**: Update column configuration persistence
15. **TASK-015**: Add filter state management to kanban views

### Phase 5: Testing and Polish
16. **TASK-016**: Test filter combinations and edge cases
17. **TASK-017**: Optimize performance for large datasets
18. **TASK-018**: Add loading states and error handling
19. **TASK-019**: Update documentation and user guides

## 3. Alternatives

- **ALT-001**: Global filter enhancement instead of column-specific - rejected because user specifically requested per-column filtering
- **ALT-002**: Separate filter panel for each column - rejected as it would consume too much screen space
- **ALT-003**: Modal-based filter interface - rejected to maintain consistency with existing dropdown pattern

## 4. Dependencies

- **DEP-001**: Existing custom fields system (already implemented)
- **DEP-002**: Folder hierarchy utilities (already implemented)
- **DEP-003**: Current kanban view and column management system
- **DEP-004**: Supabase store and task management functions

## 5. Files

- **FILE-001**: `src/types/kanbanViews.ts` - Extended with column filter types
- **FILE-002**: `src/components/ColumnFiltersSubmenu.tsx` - New filters submenu component
- **FILE-003**: `src/components/FolderTreeSelector.tsx` - New folder selection component
- **FILE-004**: `src/components/CustomFieldFilter.tsx` - New custom field filter component
- **FILE-005**: `src/components/EffortFilter.tsx` - New effort filter component
- **FILE-006**: `src/components/ColumnHeaderMenu.tsx` - Updated with filters submenu
- **FILE-007**: `src/utils/columnFilterUtils.ts` - New filter utility functions
- **FILE-008**: `src/components/KanbanBoard.tsx` - Updated to apply column filters
- **FILE-009**: `src/services/kanbanViewService.ts` - Updated to persist column filters

## 6. Testing

- **TEST-001**: Unit tests for filter utility functions
- **TEST-002**: Component tests for new filter components
- **TEST-003**: Integration tests for column filter application
- **TEST-004**: Performance tests with large task datasets
- **TEST-005**: User interaction tests for filter combinations

## 7. Risks & Assumptions

- **RISK-001**: Performance impact with complex filter combinations on large datasets
- **RISK-002**: UI complexity may overwhelm users with too many filter options
- **RISK-003**: Filter state persistence may increase data storage requirements

- **ASSUMPTION-001**: Users will primarily use 1-3 filters per column, not all available options
- **ASSUMPTION-002**: Folder hierarchy depth will remain reasonable (< 10 levels)
- **ASSUMPTION-003**: Custom fields count will remain manageable (< 20 fields)

## 8. Related Specifications / Further Reading

- [Custom Fields Implementation Guide](../docs/custom-fields-guide.md)
- [Kanban Views Type Definitions](../src/types/kanbanViews.ts)
- [Folder Utilities Documentation](../src/utils/folderUtils.ts)
- [Existing Filter Patterns](../src/components/KanbanFilters.tsx)
