import React, { useState } from 'react';
import { X } from 'lucide-react';
import { EffortFilter as EffortFilterType, ComparisonOperator, EmptyOperator } from '../types/kanbanViews';

interface EffortFilterProps {
  filter?: EffortFilterType;
  onFilterChange: (filter?: EffortFilterType) => void;
}

const EFFORT_OPERATORS: { value: EmptyOperator | ComparisonOperator; label: string }[] = [
  { value: 'empty', label: 'Is empty' },
  { value: 'not_empty', label: 'Is not empty' },
  { value: 'equal', label: 'Equals' },
  { value: 'not_equal', label: 'Does not equal' },
  { value: 'greater_than', label: 'Greater than' },
  { value: 'less_than', label: 'Less than' },
  { value: 'greater_equal', label: 'Greater than or equal' },
  { value: 'less_equal', label: 'Less than or equal' }
];

export default function EffortFilter({ filter, onFilterChange }: EffortFilterProps) {
  const [localOperator, setLocalOperator] = useState<string>(filter?.operator || '');
  const [localValue, setLocalValue] = useState<number | undefined>(filter?.value);

  const needsValue = (operator: string) => {
    return !['empty', 'not_empty'].includes(operator);
  };

  const handleOperatorChange = (operator: string) => {
    setLocalOperator(operator);
    
    if (operator === '') {
      // Clear filter
      onFilterChange(undefined);
      setLocalValue(undefined);
    } else if (!needsValue(operator)) {
      // Operator doesn't need value, apply immediately
      onFilterChange({
        operator: operator as any
      });
      setLocalValue(undefined);
    } else {
      // Operator needs value, wait for value input
      if (localValue !== undefined) {
        onFilterChange({
          operator: operator as any,
          value: localValue
        });
      }
    }
  };

  const handleValueChange = (value: number) => {
    setLocalValue(value);
    
    if (localOperator && needsValue(localOperator)) {
      onFilterChange({
        operator: localOperator as any,
        value: value
      });
    }
  };

  const clearFilter = () => {
    setLocalOperator('');
    setLocalValue(undefined);
    onFilterChange(undefined);
  };

  const getOperatorLabel = (operator: string) => {
    const operatorOption = EFFORT_OPERATORS.find(op => op.value === operator);
    return operatorOption?.label || operator;
  };

  return (
    <div className="space-y-3">
      {/* Current Filter Display */}
      {filter && (
        <div className="flex items-center gap-2 p-2 bg-blue-50 rounded">
          <div className="flex-1">
            <div className="text-xs font-medium text-blue-800">
              Effort {getOperatorLabel(filter.operator)}
              {filter.value !== undefined && ` ${filter.value} hours`}
            </div>
          </div>
          <button
            onClick={clearFilter}
            className="p-1 hover:bg-blue-100 rounded"
            title="Remove filter"
          >
            <X className="w-3 h-3 text-blue-600" />
          </button>
        </div>
      )}

      {/* Filter Configuration */}
      <div className="space-y-2">
        <div className="text-xs font-medium text-gray-700">Filter by Estimated Effort</div>
        
        {/* Operator Selection */}
        <select
          value={localOperator}
          onChange={(e) => handleOperatorChange(e.target.value)}
          className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">No filter</option>
          {EFFORT_OPERATORS.map((op) => (
            <option key={op.value} value={op.value}>
              {op.label}
            </option>
          ))}
        </select>

        {/* Value Input */}
        {localOperator && needsValue(localOperator) && (
          <div className="space-y-1">
            <label className="text-xs text-gray-600">Hours:</label>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min="0"
                step="0.5"
                value={localValue || ''}
                onChange={(e) => handleValueChange(Number(e.target.value))}
                placeholder="Enter hours"
                className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-xs text-gray-500">hrs</span>
            </div>
          </div>
        )}
      </div>

      {/* Help Text */}
      <div className="text-xs text-gray-500">
        Filter tasks based on their estimated effort in hours.
      </div>
    </div>
  );
}
