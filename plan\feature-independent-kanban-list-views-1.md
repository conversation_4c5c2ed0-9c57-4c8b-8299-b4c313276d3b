---
goal: Separate Kanban and List View Context Independence
version: 1.0
date_created: 2025-01-14
last_updated: 2025-01-14
owner: Development Team
tags: [feature, ui-ux, state-management, kanban, list-view]
---

# Introduction

This plan implements independent context management for Kanban and List views. Currently, both views share the same `selectedTreeNode` state, causing folder navigation in List view to affect task filtering in Kanban view. The goal is to make Kanban view always show all tasks (root level) with its own filters, while List view maintains independent folder navigation and filtering.

## 1. Requirements & Constraints

- **REQ-001**: Kanban view must always use all tasks (root level) as base data source
- **REQ-002**: Kanban view must apply only its own filters (project, users, groups, owners, due dates)
- **REQ-003**: List view must maintain independent folder/project navigation context
- **REQ-004**: List view must maintain independent filtering system
- **REQ-005**: Existing functionality must remain intact during transition
- **REQ-006**: No breaking changes to existing API or data structures
- **CON-001**: Must maintain backward compatibility with existing task management
- **CON-002**: Must not affect other views (<PERSON><PERSON>, Gantt, Dashboard)
- **CON-003**: Must preserve existing drag-and-drop functionality in Kanban
- **CON-004**: Must preserve existing task creation context behavior
- **GUD-001**: Follow existing state management patterns using Zustand
- **GUD-002**: Maintain consistent UI/UX patterns across views
- **PAT-001**: Use component-scoped state for view-specific context
- **PAT-002**: Preserve global state for shared functionality

## 2. Implementation Steps

### Phase 1: Create Separate Context State for List View
- **TASK-001**: Add `listViewSelectedTreeNode` to store state
- **TASK-002**: Add `setListViewSelectedTreeNode` action to store
- **TASK-003**: Update TaskListView to use new list-specific context state
- **TASK-004**: Update TaskTreeSidebar to use list-specific context when in list view

### Phase 2: Modify Kanban View to Ignore Tree Context
- **TASK-005**: Remove `selectedTreeNode` dependency from KanbanBoard component
- **TASK-006**: Update `getContextFilteredTasks` to always return all tasks
- **TASK-007**: Ensure Kanban filters work independently of tree navigation
- **TASK-008**: Update task creation context for Kanban view

### Phase 3: Update Tree Sidebar Behavior
- **TASK-009**: Modify TaskTreeSidebar to use appropriate context based on current view mode
- **TASK-010**: Update tree node selection handlers to target correct state
- **TASK-011**: Ensure tree expansion state remains shared across views

### Phase 4: Testing and Validation
- **TASK-012**: Test Kanban view shows all tasks regardless of list view navigation
- **TASK-013**: Test List view navigation doesn't affect Kanban view
- **TASK-014**: Test task creation works correctly in both views
- **TASK-015**: Test drag-and-drop functionality remains intact

## 3. Alternatives

- **ALT-001**: Create completely separate stores for each view - Rejected due to complexity and data duplication
- **ALT-002**: Use URL-based routing for view context - Rejected as it would require major architectural changes
- **ALT-003**: Add view mode parameter to all filtering functions - Rejected due to increased complexity and potential for bugs

## 4. Dependencies

- **DEP-001**: Zustand store (useSupabaseStore) - Already available
- **DEP-002**: Existing task filtering utilities - Already available
- **DEP-003**: TaskTreeSidebar component - Requires modification
- **DEP-004**: KanbanBoard component - Requires modification

## 5. Files

- **FILE-001**: `src/store/useSupabaseStore.ts` - Add list view specific state and actions
- **FILE-002**: `src/components/KanbanBoard.tsx` - Remove tree context dependency
- **FILE-003**: `src/components/TaskListView.tsx` - Use list-specific context state
- **FILE-004**: `src/components/TaskTreeSidebar.tsx` - Handle view-specific context selection
- **FILE-005**: `src/components/SupabaseSidebar.tsx` - Ensure proper context passing

## 6. Testing

- **TEST-001**: Navigate to folder in List view, switch to Kanban - verify all tasks shown
- **TEST-002**: Apply filters in Kanban view - verify only Kanban filters affect display
- **TEST-003**: Create task in Kanban view - verify proper context assignment
- **TEST-004**: Create task in List view while in folder - verify folder context preserved
- **TEST-005**: Switch between views multiple times - verify contexts remain independent
- **TEST-006**: Test drag-and-drop in Kanban view - verify functionality preserved

## 7. Risks & Assumptions

- **RISK-001**: Potential state synchronization issues between views
- **RISK-002**: Task creation context might be ambiguous in some scenarios
- **RISK-003**: Existing user workflows might be disrupted temporarily
- **ASSUMPTION-001**: Users want Kanban to always show all tasks for global overview
- **ASSUMPTION-002**: List view folder navigation is primarily for focused work
- **ASSUMPTION-003**: Current filtering systems are sufficient for both views

## 8. Related Specifications / Further Reading

- Zustand State Management Documentation
- React Component State Management Best Practices
- TaskFlow UI/UX Guidelines
