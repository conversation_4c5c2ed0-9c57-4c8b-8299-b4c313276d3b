import { Task, Folder, CustomField } from '../types';
import { 
  ColumnFilterConfig, 
  CustomFieldFilter, 
  EffortFilter, 
  FolderFilter,
  ComparisonOperator,
  EmptyOperator,
  TextOperator
} from '../types/kanbanViews';
import { getAllSubfolderIds } from './folderUtils';

/**
 * Apply column-specific filters to a list of tasks
 * @param tasks - Array of tasks to filter
 * @param filterConfig - Column filter configuration
 * @param folders - Array of all folders for hierarchy traversal
 * @param customFields - Array of custom field definitions
 * @returns Filtered array of tasks
 */
export function applyColumnFilters(
  tasks: Task[],
  filterConfig: ColumnFilterConfig,
  folders: Folder[],
  customFields: CustomField[]
): Task[] {
  if (!filterConfig.enabled) {
    return tasks;
  }

  return tasks.filter(task => {
    // Apply folder filter
    if (filterConfig.folderFilter && !passesFolderFilter(task, filterConfig.folderFilter, folders)) {
      return false;
    }

    // Apply custom field filters
    if (filterConfig.customFieldFilters.length > 0) {
      if (!passesCustomFieldFilters(task, filterConfig.customFieldFilters, customFields)) {
        return false;
      }
    }

    // Apply assignee filter
    if (filterConfig.assigneeFilter.length > 0) {
      if (!passesAssigneeFilter(task, filterConfig.assigneeFilter)) {
        return false;
      }
    }

    // Apply owner filter
    if (filterConfig.ownerFilter.length > 0) {
      if (!passesOwnerFilter(task, filterConfig.ownerFilter)) {
        return false;
      }
    }

    // Apply effort filter
    if (filterConfig.effortFilter && !passesEffortFilter(task, filterConfig.effortFilter)) {
      return false;
    }

    return true;
  });
}

/**
 * Check if task passes folder filter
 */
function passesFolderFilter(task: Task, folderFilter: FolderFilter, folders: Folder[]): boolean {
  if (folderFilter.selectedFolders.length === 0) {
    return true;
  }

  // Get task's folder ID (from project or direct folder assignment)
  const taskFolderId = task.folderId || task.projectId;
  if (!taskFolderId) {
    return false;
  }

  // Check if task's folder is in selected folders
  if (folderFilter.selectedFolders.includes(taskFolderId)) {
    return true;
  }

  // If includeSubfolders is enabled, check if task is in any subfolder
  if (folderFilter.includeSubfolders) {
    for (const selectedFolderId of folderFilter.selectedFolders) {
      const subfolderIds = getAllSubfolderIds(selectedFolderId, folders);
      if (subfolderIds.includes(taskFolderId)) {
        return true;
      }
    }
  }

  return false;
}

/**
 * Check if task passes custom field filters
 */
function passesCustomFieldFilters(
  task: Task, 
  customFieldFilters: CustomFieldFilter[], 
  customFields: CustomField[]
): boolean {
  return customFieldFilters.every(filter => {
    const customField = customFields.find(field => field.id === filter.fieldId);
    if (!customField) {
      return true; // Skip filter if field doesn't exist
    }

    const taskValue = task.customFieldValues?.[customField.name];
    return passesCustomFieldFilter(taskValue, filter, customField);
  });
}

/**
 * Check if a custom field value passes the filter
 */
function passesCustomFieldFilter(
  value: any, 
  filter: CustomFieldFilter, 
  customField: CustomField
): boolean {
  const isEmpty = value === null || value === undefined || value === '';

  // Handle empty/not empty operators
  if (filter.operator === 'empty') {
    return isEmpty;
  }
  if (filter.operator === 'not_empty') {
    return !isEmpty;
  }

  // If value is empty and we're not checking for emptiness, fail
  if (isEmpty) {
    return false;
  }

  // Handle text operators
  if (customField.fieldType === 'text' || customField.fieldType === 'dropdown') {
    return passesTextFilter(String(value), filter.operator as TextOperator, filter.value as string);
  }

  // Handle numeric operators
  if (customField.fieldType === 'number') {
    const numValue = Number(value);
    const filterValue = Number(filter.value);
    return passesNumericFilter(numValue, filter.operator as ComparisonOperator, filterValue);
  }

  // Handle date operators
  if (customField.fieldType === 'date') {
    const dateValue = new Date(value);
    const filterDate = new Date(filter.value as string);
    return passesDateFilter(dateValue, filter.operator as ComparisonOperator, filterDate);
  }

  return true;
}

/**
 * Check if text value passes text filter
 */
function passesTextFilter(value: string, operator: TextOperator, filterValue: string): boolean {
  if (!filterValue) return true;

  const lowerValue = value.toLowerCase();
  const lowerFilter = filterValue.toLowerCase();

  switch (operator) {
    case 'contains':
      return lowerValue.includes(lowerFilter);
    case 'not_contains':
      return !lowerValue.includes(lowerFilter);
    case 'equals':
      return lowerValue === lowerFilter;
    case 'not_equals':
      return lowerValue !== lowerFilter;
    default:
      return true;
  }
}

/**
 * Check if numeric value passes numeric filter
 */
function passesNumericFilter(value: number, operator: ComparisonOperator, filterValue: number): boolean {
  if (isNaN(value) || isNaN(filterValue)) return false;

  switch (operator) {
    case 'equal':
      return value === filterValue;
    case 'not_equal':
      return value !== filterValue;
    case 'greater_than':
      return value > filterValue;
    case 'less_than':
      return value < filterValue;
    case 'greater_equal':
      return value >= filterValue;
    case 'less_equal':
      return value <= filterValue;
    default:
      return true;
  }
}

/**
 * Check if date value passes date filter
 */
function passesDateFilter(value: Date, operator: ComparisonOperator, filterValue: Date): boolean {
  if (isNaN(value.getTime()) || isNaN(filterValue.getTime())) return false;

  const valueTime = value.getTime();
  const filterTime = filterValue.getTime();

  switch (operator) {
    case 'equal':
      return valueTime === filterTime;
    case 'not_equal':
      return valueTime !== filterTime;
    case 'greater_than':
      return valueTime > filterTime;
    case 'less_than':
      return valueTime < filterTime;
    case 'greater_equal':
      return valueTime >= filterTime;
    case 'less_equal':
      return valueTime <= filterTime;
    default:
      return true;
  }
}

/**
 * Check if task passes assignee filter
 */
function passesAssigneeFilter(task: Task, assigneeFilter: string[]): boolean {
  if (assigneeFilter.length === 0) {
    return true;
  }

  // Check if task has any of the selected assignees
  if (task.assignedUserId && assigneeFilter.includes(task.assignedUserId)) {
    return true;
  }

  if (task.assignedUsers && task.assignedUsers.some(userId => assigneeFilter.includes(userId))) {
    return true;
  }

  return false;
}

/**
 * Check if task passes owner filter
 */
function passesOwnerFilter(task: Task, ownerFilter: string[]): boolean {
  if (ownerFilter.length === 0) {
    return true;
  }

  return task.ownerId ? ownerFilter.includes(task.ownerId) : false;
}

/**
 * Check if task passes effort filter
 */
function passesEffortFilter(task: Task, effortFilter: EffortFilter): boolean {
  const taskEffort = task.estimatedEffort;
  const isEmpty = taskEffort === null || taskEffort === undefined;

  // Handle empty/not empty operators
  if (effortFilter.operator === 'empty') {
    return isEmpty;
  }
  if (effortFilter.operator === 'not_empty') {
    return !isEmpty;
  }

  // If task effort is empty and we're not checking for emptiness, fail
  if (isEmpty) {
    return false;
  }

  // Handle numeric comparison
  if (effortFilter.value !== undefined) {
    return passesNumericFilter(taskEffort, effortFilter.operator as ComparisonOperator, effortFilter.value);
  }

  return true;
}

/**
 * Create a default column filter configuration
 */
export function createDefaultColumnFilter(): ColumnFilterConfig {
  return {
    customFieldFilters: [],
    assigneeFilter: [],
    ownerFilter: [],
    enabled: false
  };
}

/**
 * Check if column filter has any active filters
 */
export function hasActiveFilters(filterConfig: ColumnFilterConfig): boolean {
  if (!filterConfig.enabled) {
    return false;
  }

  return (
    (filterConfig.folderFilter?.selectedFolders.length ?? 0) > 0 ||
    filterConfig.customFieldFilters.length > 0 ||
    filterConfig.assigneeFilter.length > 0 ||
    filterConfig.ownerFilter.length > 0 ||
    filterConfig.effortFilter !== undefined
  );
}
