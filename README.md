# Project Management Tool

A modern, feature-rich project management application built with React, TypeScript, and Supabase.

## 🚀 Features

- **Task Management**: Create, edit, Filter and track tasks with priorities, share taks with links, assignments, estimate effort
- **Multi level Organization**: Tree view with folders, subfolders, projects and tasks in unlimited depth. Supports drag n drop for rearanging 
- **Custom Fields**: Create custom text, number, date, and dropdown fields for tasks and subtasks
- **Task Dependencies**: Define finish-to-start dependencies between tasks with automatic date calculations
- **Subtask Support**: Break down tasks into manageable subtasks with full comment support
- **Project Organization**: Organize tasks into projects and folders with hierarchical structure
- **Kanban Board**: Fully customized, Visual task management with drag-and-drop functionality. Filters per column, Custom kanban views and sharing between users. Compact and extended views.
- **Gantt Chart**: Timeline visualization with dependency connectors (visual optimizations pending)
- **User Management**: Team collaboration with user roles, groups, and skillset management
- **Rich Text Editor**: Enhanced task and subtask descriptions with formatting, lists, links, code, and strikethrough
- **Resource Capacity Management**: Track user availability, working hours, and capacity planning
- **Comments & History**: Track task progress with threaded comments and detailed change history
- **Time Tracking**: Built-in duration tracking for tasks and subtasks
- **Authentication**: Secure user authentication with email confirmation
- **Real-time Collaboration**: Live updates and synchronization across all users
- **In-app notification center with read/unread status.** : @mentions in comments
- **Clone Functionality**: Clone tasks and projects with all attributes, dependencies, and custom fields
- **Archive System**: Soft-delete functionality with restore capabilities
- **Set Recurring Tasks**: Ability to schedule tasks for auto creation
- **Analytics & Reporting**: Estimated vs Actual effort analysis per task, project, team, individual, skillset



## 👁️ What's next - Todo

- **Gantt Chart Visual Optimizations**: Fix dependency connector positioning and improve visual alignment
- **Subscription/watch functionality for tasks**: notiification in inbox section
- **Bulk import Projects and Tasks**: Use csv or json files to bulk import projects and tasks
- **More Reporting capabilities**: Custom Reports - time on status, full BI
- **More user level Time tracking capabilities**: Manual options on individual levels, audits, reporting
- **Enhanced Custom Fields**: Add filtering and reporting capabilities for custom field values (list view and kanban view)
- **AI power assistant and Project Management**: Having and Assigning AI agents for various jobs
- **Enhanced user roles and permisions settings**: More flexibility to set persmissions across all functionality
- **Create Full API interface**: Endpoints to support task/project/folder creation/edit, comments post/read, e.t.c
- **Escalation rules**: Ability to set escalation rules for tasks and projects
- **Real time Security monitoring**: Filter for specific words used in tasks and comments (plain passwords, e.t.c) 
- **Webhooks in automation actions**: Add the options of calling webhooks from automation actions 
- **Smart Subscription Management**: Real-time Architecture Redesign - Connection pooling
- **Add React Query for intelligent data caching**: Performance optimization



## 🐛 Known Issues

- [Fixed] Task history entries: For certain history entries in tasks, previous state is reported as empty
- [Fixed] Task history real-time updates: History entries now appear immediately without page refresh
- [Fixed] User information in history: History entries now show user avatars and names
- [Fixed] User avatar not displayed: User avatars now displayed correctly
- [Fixed] Inbox notifications: View task link does not work
- [Fixed] Task sharing: Share links now preserve URL parameters through authentication
- Canvas for an existing automation seems empty


## 🛠 Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time, Storage)
- **State Management**: Zustand with Supabase integration
- **Rich Text Editor**: React Quill with custom styling and formatting
- **UI Components**: Lucide React icons, custom components
- **Date Handling**: date-fns
- **Database**: PostgreSQL with Row Level Security (RLS)

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account and project

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd project-management-tool
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env.local
```

Configure your Supabase credentials in `.env.local`:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Database Setup

**⚠️ Critical**: Run these SQL files in your Supabase SQL editor in **exact order**:

1. `supabase-schema.sql` - Core database schema and tables
2. `supabase-rls-policies.sql` - Row Level Security policies
3. `supabase-triggers.sql` - User creation triggers and functions
4. `supabase-add-subtasks.sql` - Subtask support
5. `supabase-task-dependencies.sql` - Task dependencies functionality
6. `supabase-custom-fields.sql` - Custom fields functionality
7. `supabase-archive-schema.sql` - Archive system for soft-delete functionality
8. `supabase-recurring-tasks-schema.sql` - Recurring tasks functionality
9. `supabase-kanban-views-schema.sql` - Custom kanban views functionality

### 4. Start Development

```bash
npm run dev
```

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── auth/           # Authentication components
│   ├── forms/          # Form components (Task, Project, User)
│   ├── RichTextEditor.tsx    # Rich text editor component
│   ├── RichTextDisplay.tsx   # Rich text display components
│   └── ui/             # Reusable UI components
├── contexts/           # React contexts (Auth)
├── lib/                # Utility libraries (Supabase client)
├── services/           # API services and data access
├── store/              # Zustand stores with Supabase integration
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
│   └── richTextUtils.ts      # Rich text conversion utilities
└── docs/               # Documentation files
```

## 📚 Documentation

- [Database Setup Guide](./SUPABASE_SETUP.md) - Detailed database setup
- [Supabase Integration](./README_SUPABASE.md) - Supabase-specific documentation
- [Rich Text Editor Guide](./docs/rich-text-editor-guide.md) - Complete rich text editing functionality
- [Task Dependencies Guide](./docs/task-dependencies-guide.md) - Complete dependency management documentation
- [Resource Capacity Management](./docs/resource-capacity-management-guide.md) - Capacity planning features
- [Multi-User Collaboration Guide](./docs/multi-user-collaboration-guide.md) - Security, permissions, and real-time sync
- [Real-time Collaboration Guide](./docs/real-time-collaboration-guide.md) - Comprehensive real-time features and troubleshooting
- [Multi-User Troubleshooting](./docs/troubleshooting-multi-user.md) - Common issues and quick fixes
- [Custom Fields Implementation](./CUSTOM_FIELDS_IMPLEMENTATION.md) - Complete custom fields functionality guide
- [Clone Functionality Guide](./docs/clone-functionality-guide.md) - Complete guide to cloning tasks and projects
- [Clone Quick Reference](./docs/clone-quick-reference.md) - Quick access guide for clone functionality
- [Archive System Guide](./docs/archive-system-guide.md) - Complete guide to archive and restore functionality
- [Archive Quick Reference](./docs/archive-quick-reference.md) - Quick access guide for archive operations
- [Task Sharing Guide](./docs/task-sharing-guide.md) - Complete guide to sharing tasks with direct links
- [Recent Improvements Guide](./docs/recent-improvements-guide.md) - Latest updates to task history and comment systems

## 🔧 Key Features

### Authentication
- Email/password authentication via Supabase Auth
- Automatic user profile creation on signup
- Admin and user roles with proper permissions

### Task Management
- Full CRUD operations for tasks and subtasks
- Priority levels, due dates, and status tracking
- Custom fields (text, number, date, dropdown) for tasks and subtasks
- Task dependencies with automatic date calculations
- Assignee management with user groups and skillsets
- Threaded comments on tasks and subtasks with real-time updates
- Task history and change tracking with user information and real-time sync
- **Clone Tasks**: Clone individual tasks with all properties and subtasks
  - Available from task list view (Copy button)
  - Available from task edit form (three-dot menu)
  - Available from subtask edit form (clone parent task)
- **Share Tasks**: Generate shareable links for direct task access
  - Available from task edit form (three-dot menu → Share Task)
  - Available from subtask edit form (three-dot menu → Share Subtask)
  - Links work across browser sessions and preserve authentication state

### Project Organization
- Hierarchical folder and project structure
- Task filtering and organization
- Kanban board with drag-and-drop
- List view with advanced filtering
- Gantt chart with timeline visualization and dependency tracking
- **Clone Projects**: Clone entire projects with all tasks and dependencies
  - Available from project context menus (right-click)
  - Available from project manager (hover Copy button)
  - Preserves all project properties (dates, folder, color)
  - Clones all contained tasks and subtasks
  - Maintains task dependencies within the cloned project
- **Archive Management**: Soft-delete system for projects and folders
  - Delete projects and folders with cascade archiving of all contents
  - Admin-only archive management interface for viewing and restoring
  - 1-week retention with automatic cleanup of old archived items
  - Maintains original hierarchy for proper restoration

### User & Resource Management
- User groups and skillset management
- Capacity planning with working hours
- Resource allocation and availability tracking
- Admin controls for user management

## 🐛 Troubleshooting

### Common Issues

1. **User registration fails**: Ensure triggers are properly installed
2. **Profile update errors**: Check RLS policies are applied
3. **Subtasks not saving**: Verify subtasks column exists
4. **Authentication issues**: Check Supabase URL and keys

### Database Issues

If you encounter database errors:
1. Check all SQL files were run in correct order
2. Verify RLS policies are enabled
3. Ensure triggers are active
4. Check foreign key constraints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly (especially database operations)
5. Update documentation if needed
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the database setup guide
3. Check Supabase console for errors
4. Create an issue with detailed error information
