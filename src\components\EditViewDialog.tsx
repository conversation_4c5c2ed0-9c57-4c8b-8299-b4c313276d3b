import React, { useState, useEffect } from 'react';
import { X, Save, Edit3 } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { KanbanView } from '../types/kanbanViews';

interface EditViewDialogProps {
  isOpen: boolean;
  view: KanbanView | null;
  onClose: () => void;
  onSaved?: () => void;
}

export default function EditViewDialog({ isOpen, view, onClose, onSaved }: EditViewDialogProps) {
  const { updateKanbanView } = useSupabaseStore();
  
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize form data when view changes
  useEffect(() => {
    if (view) {
      setFormData({
        name: view.name,
        description: view.description || ''
      });
    }
  }, [view]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!view) return;
    
    if (!formData.name.trim()) {
      setError('View name is required');
      return;
    }

    // Check if anything actually changed
    const hasChanges = 
      formData.name.trim() !== view.name ||
      formData.description.trim() !== (view.description || '');

    if (!hasChanges) {
      handleClose();
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const updatedView = await updateKanbanView(view.id, {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined
      });

      if (updatedView) {
        onSaved?.();
        handleClose();
      } else {
        setError('Failed to update view. Please try again.');
      }
    } catch (error) {
      console.error('Failed to update view:', error);
      setError(error.message || 'Failed to update view. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setError(null);
      onClose();
    }
  };

  if (!isOpen || !view) return null;

  const canEdit = !view.isShared || view.permissionLevel === 'write' || view.permissionLevel === 'admin';

  if (!canEdit) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Edit3 className="w-5 h-5 text-gray-600" />
              <h2 className="text-lg font-semibold text-gray-900">View Details</h2>
            </div>
            <button
              onClick={handleClose}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
          
          <div className="p-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                You don't have permission to edit this shared view. You can only view and use it.
              </p>
            </div>
            
            <div className="mt-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">View Name</label>
                <p className="mt-1 text-sm text-gray-900">{view.name}</p>
              </div>
              
              {view.description && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <p className="mt-1 text-sm text-gray-900">{view.description}</p>
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Shared by</label>
                <p className="mt-1 text-sm text-gray-900">{view.sharedByEmail || 'Unknown'}</p>
              </div>
            </div>
            
            <div className="flex justify-end mt-6">
              <button
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Edit3 className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Edit View</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* View Name */}
            <div>
              <label htmlFor="editViewName" className="block text-sm font-medium text-gray-700 mb-1">
                View Name *
              </label>
              <input
                id="editViewName"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter view name..."
                disabled={isLoading || view.isDefault}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-50"
                maxLength={100}
                required
              />
              {view.isDefault && (
                <p className="text-xs text-gray-500 mt-1">
                  Default view name cannot be changed
                </p>
              )}
              <div className="text-xs text-gray-500 mt-1">
                {formData.name.length}/100 characters
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="editViewDescription" className="block text-sm font-medium text-gray-700 mb-1">
                Description (Optional)
              </label>
              <textarea
                id="editViewDescription"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this view is for..."
                disabled={isLoading}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-50 resize-none"
                maxLength={500}
              />
              <div className="text-xs text-gray-500 mt-1">
                {formData.description.length}/500 characters
              </div>
            </div>

            {/* View Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">View Information:</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Created: {new Date(view.createdAt).toLocaleDateString()}</div>
                <div>Last used: {new Date(view.lastUsedAt).toLocaleDateString()}</div>
                <div>Usage count: {view.usageCount} times</div>
                {view.isDefault && (
                  <div className="text-blue-600 font-medium">This is your default view</div>
                )}
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.name.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
