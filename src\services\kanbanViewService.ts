import { supabase, handleSupabaseError, getCurrentUser } from '../lib/supabase';
import {
  KanbanView,
  KanbanViewShare,
  CreateKanbanViewInput,
  UpdateKanbanViewInput,
  ShareKanbanViewInput,
  KanbanViewOperationResult,
  KanbanViewShareResult,
  KanbanViewList,
  DEFAULT_FILTER_CONFIG,
  DEFAULT_COLUMN_CONFIG,
  DEFAULT_VIEW_SETTINGS
} from '../types/kanbanViews';

class KanbanViewService {
  // Get all views accessible to the current user
  async getUserViews(): Promise<KanbanViewList> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      // Get owned views
      const { data: ownedViews, error: ownedError } = await supabase
        .from('kanban_views')
        .select('*')
        .eq('created_by', user.id)
        .order('last_used_at', { ascending: false });

      if (ownedError) handleSupabaseError(ownedError);

      // Get shared views
      const { data: sharedViewsData, error: sharedError } = await supabase
        .from('kanban_view_shares')
        .select(`
          *,
          kanban_views (*)
        `)
        .eq('shared_with_user_id', user.id);

      if (sharedError) handleSupabaseError(sharedError);

      const sharedViews = sharedViewsData?.map(share => ({
        ...share.kanban_views,
        isShared: true,
        sharedBy: share.shared_by_user_id,
        permissionLevel: share.permission_level
      })) || [];

      const defaultView = ownedViews?.find(view => view.is_default);

      return {
        ownedViews: ownedViews?.map(this.transformSupabaseView) || [],
        sharedViews: sharedViews.map(this.transformSupabaseView),
        defaultView: defaultView ? this.transformSupabaseView(defaultView) : undefined
      };
    } catch (error) {
      console.error('Failed to fetch user views:', error);
      throw error;
    }
  }

  // Get a specific view by ID
  async getView(viewId: string): Promise<KanbanView | null> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase
        .from('kanban_views')
        .select('*')
        .eq('id', viewId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        handleSupabaseError(error);
      }

      return data ? this.transformSupabaseView(data) : null;
    } catch (error) {
      console.error('Failed to fetch view:', error);
      throw error;
    }
  }

  // Create a new view
  async createView(input: CreateKanbanViewInput): Promise<KanbanViewOperationResult> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase
        .from('kanban_views')
        .insert({
          name: input.name,
          description: input.description,
          created_by: user.id,
          filter_config: input.filterConfig,
          column_config: input.columnConfig,
          view_settings: input.viewSettings,
          is_default: false
        })
        .select()
        .single();

      if (error) handleSupabaseError(error);

      return {
        success: true,
        view: this.transformSupabaseView(data)
      };
    } catch (error) {
      console.error('Failed to create view:', error);
      return {
        success: false,
        error: error.message || 'Failed to create view'
      };
    }
  }

  // Update an existing view
  async updateView(viewId: string, input: UpdateKanbanViewInput): Promise<KanbanViewOperationResult> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      // Get current view for optimistic locking
      const { data: currentView, error: fetchError } = await supabase
        .from('kanban_views')
        .select('version, created_by')
        .eq('id', viewId)
        .single();

      if (fetchError) handleSupabaseError(fetchError);
      if (!currentView) throw new Error('View not found');

      // Check permissions
      if (currentView.created_by !== user.id) {
        throw new Error('You do not have permission to update this view');
      }

      const updateData: any = {
        updated_by: user.id,
        updated_at: new Date().toISOString()
      };

      if (input.name !== undefined) updateData.name = input.name;
      if (input.description !== undefined) updateData.description = input.description;
      if (input.filterConfig !== undefined) {
        updateData.filter_config = {
          ...currentView.filter_config,
          ...input.filterConfig
        };
      }
      if (input.columnConfig !== undefined) {
        updateData.column_config = {
          ...currentView.column_config,
          ...input.columnConfig
        };
      }
      if (input.viewSettings !== undefined) {
        updateData.view_settings = {
          ...currentView.view_settings,
          ...input.viewSettings
        };
      }

      const { data, error } = await supabase
        .from('kanban_views')
        .update(updateData)
        .eq('id', viewId)
        .eq('version', currentView.version)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error('View was modified by another user. Please refresh and try again.');
        }
        handleSupabaseError(error);
      }

      return {
        success: true,
        view: this.transformSupabaseView(data)
      };
    } catch (error) {
      console.error('Failed to update view:', error);
      return {
        success: false,
        error: error.message || 'Failed to update view'
      };
    }
  }

  // Delete a view
  async deleteView(viewId: string): Promise<KanbanViewOperationResult> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      const { error } = await supabase
        .from('kanban_views')
        .delete()
        .eq('id', viewId)
        .eq('created_by', user.id);

      if (error) handleSupabaseError(error);

      return { success: true };
    } catch (error) {
      console.error('Failed to delete view:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete view'
      };
    }
  }

  // Share a view with another user
  async shareView(input: ShareKanbanViewInput): Promise<KanbanViewShareResult> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      // Find user by email
      const { data: targetUser, error: userError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('email', input.userEmail)
        .single();

      if (userError || !targetUser) {
        return {
          success: false,
          error: 'User not found with that email address'
        };
      }

      // Check if view exists and user owns it
      const { data: view, error: viewError } = await supabase
        .from('kanban_views')
        .select('created_by')
        .eq('id', input.viewId)
        .single();

      if (viewError || !view) {
        return {
          success: false,
          error: 'View not found'
        };
      }

      if (view.created_by !== user.id) {
        return {
          success: false,
          error: 'You do not have permission to share this view'
        };
      }

      // Create or update share
      const { data, error } = await supabase
        .from('kanban_view_shares')
        .upsert({
          view_id: input.viewId,
          shared_with_user_id: targetUser.id,
          shared_by_user_id: user.id,
          permission_level: input.permissionLevel
        })
        .select()
        .single();

      if (error) handleSupabaseError(error);

      return {
        success: true,
        share: this.transformSupabaseShare(data)
      };
    } catch (error) {
      console.error('Failed to share view:', error);
      return {
        success: false,
        error: error.message || 'Failed to share view'
      };
    }
  }

  // Update view usage statistics
  async updateViewUsage(viewId: string): Promise<void> {
    const user = await getCurrentUser();
    if (!user) return;

    try {
      await supabase.rpc('update_view_usage_stats', {
        view_id: viewId,
        user_id: user.id
      });
    } catch (error) {
      console.error('Failed to update view usage:', error);
      // Don't throw - this is not critical
    }
  }

  // Create default view for new user
  async createDefaultView(): Promise<KanbanView | null> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase.rpc('create_default_kanban_view_for_user', {
        user_id: user.id
      });

      if (error) handleSupabaseError(error);

      if (data) {
        const view = await this.getView(data);
        return view;
      }

      return null;
    } catch (error) {
      console.error('Failed to create default view:', error);
      throw error;
    }
  }

  // Transform Supabase data to our types
  private transformSupabaseView(data: any): KanbanView {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      isDefault: data.is_default,
      createdBy: data.created_by,
      filterConfig: data.filter_config || DEFAULT_FILTER_CONFIG,
      columnConfig: data.column_config || DEFAULT_COLUMN_CONFIG,
      viewSettings: data.view_settings || DEFAULT_VIEW_SETTINGS,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      lastUsedAt: data.last_used_at,
      usageCount: data.usage_count || 0,
      version: data.version || 1,
      updatedBy: data.updated_by,
      isShared: data.isShared,
      sharedBy: data.sharedBy,
      permissionLevel: data.permissionLevel
    };
  }

  private transformSupabaseShare(data: any): KanbanViewShare {
    return {
      id: data.id,
      viewId: data.view_id,
      sharedWithUserId: data.shared_with_user_id,
      sharedByUserId: data.shared_by_user_id,
      permissionLevel: data.permission_level,
      sharedAt: data.shared_at,
      lastAccessedAt: data.last_accessed_at,
      accessCount: data.access_count || 0
    };
  }
}

export const kanbanViewService = new KanbanViewService();
