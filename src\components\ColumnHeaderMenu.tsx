import React, { useState } from 'react';
import { ChevronDown, ArrowUpDown, Palette, Edit3, Calendar, Zap, GripVertical, ArrowLeft, ArrowRight, Filter } from 'lucide-react';
import ColumnFiltersSubmenu from './ColumnFiltersSubmenu';
import { DEFAULT_COLUMN_FILTER_CONFIG } from '../types/kanbanViews';

interface ColumnConfig {
  id: string;
  title: string;
  color: string;
  sortBy: 'none' | 'dueDate' | 'priority' | 'title' | 'created';
  sortOrder: 'asc' | 'desc';
  filterConfig?: import('../types/kanbanViews').ColumnFilterConfig;
}

interface ColumnHeaderMenuProps {
  column: {
    id: string;
    title: string;
    color: string;
  };
  config: ColumnConfig;
  onConfigChange: (config: ColumnConfig) => void;
  onMoveLeft?: () => void;
  onMoveRight?: () => void;
  canMoveLeft?: boolean;
  canMoveRight?: boolean;
}

const SORT_OPTIONS = [
  { value: 'none', label: 'No sorting', icon: null },
  { value: 'dueDate', label: 'Due date', icon: Calendar },
  { value: 'priority', label: 'Priority', icon: Zap },
  { value: 'title', label: 'Title (A-Z)', icon: Edit3 },
  { value: 'created', label: 'Created date', icon: Calendar },
];

const COLOR_OPTIONS = [
  { value: 'bg-gray-100', label: 'Gray', color: '#f3f4f6' },
  { value: 'bg-blue-100', label: 'Blue', color: '#dbeafe' },
  { value: 'bg-green-100', label: 'Green', color: '#dcfce7' },
  { value: 'bg-yellow-100', label: 'Yellow', color: '#fef3c7' },
  { value: 'bg-red-100', label: 'Red', color: '#fee2e2' },
  { value: 'bg-purple-100', label: 'Purple', color: '#f3e8ff' },
  { value: 'bg-pink-100', label: 'Pink', color: '#fce7f3' },
  { value: 'bg-indigo-100', label: 'Indigo', color: '#e0e7ff' },
];

export default function ColumnHeaderMenu({
  column,
  config,
  onConfigChange,
  onMoveLeft,
  onMoveRight,
  canMoveLeft = true,
  canMoveRight = true
}: ColumnHeaderMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editTitle, setEditTitle] = useState(config.title);
  const [showFilters, setShowFilters] = useState(false);

  const handleSortChange = (sortBy: string) => {
    const newSortOrder = config.sortBy === sortBy && config.sortOrder === 'asc' ? 'desc' : 'asc';
    onConfigChange({
      ...config,
      sortBy: sortBy as ColumnConfig['sortBy'],
      sortOrder: newSortOrder
    });
  };

  const handleColorChange = (color: string) => {
    onConfigChange({
      ...config,
      color
    });
    setIsOpen(false);
  };

  const handleFilterChange = (filterConfig: import('../types/kanbanViews').ColumnFilterConfig) => {
    onConfigChange({
      ...config,
      filterConfig
    });
  };

  const handleTitleSave = () => {
    if (editTitle.trim() && editTitle.trim() !== config.title) {
      onConfigChange({
        ...config,
        title: editTitle.trim()
      });
    }
    setIsEditingTitle(false);
    setIsOpen(false);
  };

  const handleTitleCancel = () => {
    setEditTitle(config.title);
    setIsEditingTitle(false);
  };

  const currentColor = COLOR_OPTIONS.find(opt => opt.value === config.color);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-1 hover:bg-gray-200 rounded transition-colors"
        title="Column options"
      >
        <ChevronDown className={`w-5 h-5 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Menu */}
          <div className="absolute top-full right-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            <div className="p-3">
              {/* Edit Title Section */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Column Title</span>
                  {!isEditingTitle && (
                    <button
                      onClick={() => setIsEditingTitle(true)}
                      className="p-1 hover:bg-gray-100 rounded"
                      title="Edit title"
                    >
                      <Edit3 className="w-3 h-3" />
                    </button>
                  )}
                </div>
                
                {isEditingTitle ? (
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Column title"
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleTitleSave();
                        if (e.key === 'Escape') handleTitleCancel();
                      }}
                    />
                    <div className="flex gap-2">
                      <button
                        onClick={handleTitleSave}
                        className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        Save
                      </button>
                      <button
                        onClick={handleTitleCancel}
                        className="px-2 py-1 text-xs bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-600 bg-gray-50 px-2 py-1 rounded">
                    {config.title}
                  </div>
                )}
              </div>

              {/* Column Position Section */}
              {(onMoveLeft || onMoveRight) && (
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <GripVertical className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">Column Position</span>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={onMoveLeft}
                      disabled={!canMoveLeft}
                      className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded transition-colors ${
                        canMoveLeft
                          ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                          : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      }`}
                      title={canMoveLeft ? 'Move column left' : 'Cannot move further left'}
                    >
                      <ArrowLeft className="w-3 h-3" />
                      Move Left
                    </button>

                    <button
                      onClick={onMoveRight}
                      disabled={!canMoveRight}
                      className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded transition-colors ${
                        canMoveRight
                          ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                          : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      }`}
                      title={canMoveRight ? 'Move column right' : 'Cannot move further right'}
                    >
                      Move Right
                      <ArrowRight className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              )}

              {/* Sort Section */}
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <ArrowUpDown className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Sort Tasks</span>
                </div>

                <div className="space-y-1">
                  {SORT_OPTIONS.map((option) => {
                    const Icon = option.icon;
                    const isActive = config.sortBy === option.value;

                    return (
                      <button
                        key={option.value}
                        onClick={() => handleSortChange(option.value)}
                        className={`w-full flex items-center gap-2 px-2 py-1.5 text-sm rounded transition-colors ${
                          isActive
                            ? 'bg-blue-50 text-blue-700 border border-blue-200'
                            : 'hover:bg-gray-50 text-gray-700'
                        }`}
                      >
                        {Icon && <Icon className="w-3 h-3" />}
                        <span className="flex-1 text-left">{option.label}</span>
                        {isActive && config.sortBy !== 'none' && (
                          <span className="text-xs text-blue-600">
                            {config.sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Filters Section */}
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Column Filters</span>
                  {config.filterConfig?.enabled && (
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      Active
                    </span>
                  )}
                </div>

                <button
                  onClick={() => setShowFilters(true)}
                  className="w-full flex items-center gap-2 px-2 py-1.5 text-sm rounded transition-colors hover:bg-gray-50 text-gray-700"
                >
                  <Filter className="w-3 h-3" />
                  <span className="flex-1 text-left">Configure Filters</span>
                </button>
              </div>

              {/* Color Section */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Palette className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Background Color</span>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {COLOR_OPTIONS.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleColorChange(option.value)}
                      className={`w-4 h-4 rounded border transition-all ${
                        config.color === option.value
                          ? 'border-gray-600 ring-1 ring-blue-500 ring-offset-1'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      style={{ backgroundColor: option.color }}
                      title={option.label}
                    />
                  ))}
                </div>
                
                {currentColor && (
                  <div className="mt-2 text-xs text-gray-500 text-center">
                    Current: {currentColor.label}
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Column Filters Submenu */}
      {showFilters && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setShowFilters(false)}
          />

          <ColumnFiltersSubmenu
            filterConfig={config.filterConfig || DEFAULT_COLUMN_FILTER_CONFIG}
            onFilterChange={handleFilterChange}
            onClose={() => setShowFilters(false)}
          />
        </>
      )}
    </div>
  );
}
