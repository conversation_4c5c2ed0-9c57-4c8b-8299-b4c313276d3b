---
goal: Implement Drag and Drop Tree Rearrangement for Folders, Projects, and Tasks
version: 1.0
date_created: 2025-09-12
last_updated: 2025-09-12
owner: Development Team
tags: [feature, ui-enhancement, drag-drop, tree-navigation, folder-management]
---

# Introduction

This plan implements drag and drop functionality for the task tree view to allow users to rearrange folders, projects, and tasks by dragging and dropping them to new locations. When moving a folder, all its contents (subfolders, projects, tasks) will move together as a unit.

## 1. Requirements & Constraints

- **REQ-001**: Implement native HTML5 drag and drop API (no external libraries needed)
- **REQ-002**: Support dragging folders, projects, and tasks within the tree structure
- **REQ-003**: When moving folders, move entire hierarchy (subfolders, projects, tasks) together
- **REQ-004**: Provide visual feedback during drag operations (drag preview, drop zones)
- **REQ-005**: Validate drop targets to prevent invalid moves (e.g., folder into its own subfolder)
- **REQ-006**: Update database relationships when items are moved
- **REQ-007**: Maintain tree expansion state after moves
- **SEC-001**: Validate user permissions for moving items
- **SEC-002**: Ensure data integrity during move operations
- **CON-001**: Must not break existing tree functionality (expand/collapse, selection)
- **CON-002**: Must work with existing TaskTreeSidebar and TaskTreeNode components
- **CON-003**: Performance must remain acceptable with large tree structures
- **GUD-001**: Follow existing drag and drop patterns from KanbanBoard component
- **GUD-002**: Use consistent visual styling with current dark theme
- **PAT-001**: Use existing Supabase store patterns for data updates
- **PAT-002**: Implement proper error handling and user feedback

## 2. Implementation Steps

### Phase 1: Core Drag and Drop Infrastructure
1. **TASK-001**: Add drag and drop state management to TaskTreeSidebar
   - Add state for draggedItem, dragOverItem, dropPosition
   - Implement drag start, drag over, and drop event handlers
   - Add visual feedback for drag operations

2. **TASK-002**: Update TaskTreeNode to support drag and drop
   - Add draggable attribute and drag event handlers
   - Implement drop zone visual indicators
   - Add drag handle styling and cursor changes

### Phase 2: Move Logic Implementation
3. **TASK-003**: Create utility functions for tree operations
   - Implement validateDropTarget() to prevent invalid moves
   - Create moveTreeItem() function for updating parent relationships
   - Add getAllDescendantIds() for moving folder hierarchies

4. **TASK-004**: Add store methods for moving items
   - Implement moveFolder() in Supabase store
   - Add moveProject() method
   - Create moveTask() method (if not already exists)
   - Add batch update functionality for folder hierarchies

### Phase 3: Database Integration
5. **TASK-005**: Update Supabase service methods
   - Add folder move operations to folderService
   - Update project move operations in projectService
   - Ensure task move operations handle folder changes
   - Implement transaction-based moves for data integrity

6. **TASK-006**: Add validation and error handling
   - Implement circular dependency detection
   - Add permission checks for move operations
   - Create rollback mechanisms for failed moves
   - Add user feedback for move operations

## 3. Alternatives

- **ALT-001**: External drag and drop library (react-dnd) - Rejected due to bundle size and complexity
- **ALT-002**: Context menu based move operations - Rejected as less intuitive than drag and drop
- **ALT-003**: Modal-based move interface - Rejected as it breaks the direct manipulation UX
- **ALT-004**: Copy instead of move operations - Rejected as it doesn't match user expectations

## 4. Dependencies

- **DEP-001**: Existing TaskTreeSidebar and TaskTreeNode components
- **DEP-002**: Current folder/project/task data models and relationships
- **DEP-003**: Supabase store implementation and service methods
- **DEP-004**: HTML5 drag and drop API support in target browsers

## 5. Files

- **FILE-001**: src/components/TaskTreeSidebar.tsx - Add drag and drop state and handlers
- **FILE-002**: src/components/TaskTreeNode.tsx - Add draggable functionality and drop zones
- **FILE-003**: src/utils/treeUtils.ts - New utility for tree manipulation operations
- **FILE-004**: src/store/useSupabaseStore.ts - Add move methods for folders/projects/tasks
- **FILE-005**: src/services/supabaseService.ts - Update service methods for move operations
- **FILE-006**: src/utils/folderUtils.ts - Extend with move validation functions

## 6. Testing

- **TEST-001**: Test dragging folders between different parent folders
- **TEST-002**: Verify entire folder hierarchy moves together when folder is dragged
- **TEST-003**: Test dragging projects between folders and to root level
- **TEST-004**: Test dragging tasks between projects and folders
- **TEST-005**: Verify invalid drop targets are rejected (circular dependencies)
- **TEST-006**: Test performance with large tree structures during drag operations
- **TEST-007**: Verify tree expansion state is maintained after moves
- **TEST-008**: Test error handling for failed move operations

## 7. Risks & Assumptions

- **RISK-001**: Complex folder hierarchies may cause performance issues during validation
- **RISK-002**: Concurrent users moving same items could cause data conflicts
- **RISK-003**: Browser compatibility issues with HTML5 drag and drop API
- **RISK-004**: Database transaction failures could leave data in inconsistent state
- **ASSUMPTION-001**: Users have appropriate permissions to move items they can see
- **ASSUMPTION-002**: Tree structure depth is reasonable (not extremely deep)
- **ASSUMPTION-003**: Network latency won't significantly impact drag and drop UX

## 8. Related Specifications / Further Reading

- HTML5 Drag and Drop API documentation
- Existing KanbanBoard drag and drop implementation
- TaskTreeSidebar component architecture
- Supabase real-time updates and conflict resolution
