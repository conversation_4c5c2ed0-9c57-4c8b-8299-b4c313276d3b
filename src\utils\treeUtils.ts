import { Folder, Project, Task } from '../types';

export interface MoveOperation {
  draggedItem: {
    id: string;
    type: 'folder' | 'project' | 'task';
  };
  targetId: string;
  targetType: 'folder' | 'project' | 'task';
  position: 'before' | 'after' | 'inside';
}

export interface MoveResult {
  isValid: boolean;
  error?: string;
  newParentId?: string | null;
  newPosition?: number;
}

/**
 * Validate if a move operation is allowed
 */
export function validateMoveOperation(
  operation: MoveOperation,
  folders: Folder[],
  projects: Project[],
  tasks: Task[]
): MoveResult {
  const { draggedItem, targetId, targetType, position } = operation;

  // Can't drop on itself
  if (draggedItem.id === targetId) {
    return { isValid: false, error: 'Cannot move item to itself' };
  }

  // Check for circular dependencies when moving folders
  if (draggedItem.type === 'folder' && targetType === 'folder') {
    if (wouldCreateCircularDependency(draggedItem.id, targetId, folders)) {
      return { isValid: false, error: 'Cannot move folder into its own subfolder' };
    }
  }

  // Determine the new parent based on the move operation
  let newParentId: string | null = null;

  if (position === 'inside') {
    // Can only drop inside folders
    if (targetType !== 'folder') {
      return { isValid: false, error: 'Can only drop items inside folders' };
    }
    newParentId = targetId;
  } else {
    // For 'before' and 'after', get the parent of the target
    const targetParent = getItemParent(targetId, targetType, folders, projects, tasks);
    newParentId = targetParent;
  }

  // Validate type-specific rules
  switch (draggedItem.type) {
    case 'folder':
      // Folders can be moved anywhere
      break;
    case 'project':
      // Projects can only be in folders or at root level
      break;
    case 'task':
      // Tasks can be in projects or folders
      break;
  }

  return { isValid: true, newParentId };
}

/**
 * Check if moving a folder would create a circular dependency
 */
function wouldCreateCircularDependency(
  folderId: string,
  targetFolderId: string,
  folders: Folder[]
): boolean {
  // Check if targetFolderId is a descendant of folderId
  const descendants = getAllDescendantFolderIds(folderId, folders);
  return descendants.includes(targetFolderId);
}

/**
 * Get all descendant folder IDs for a given folder
 */
export function getAllDescendantFolderIds(folderId: string, folders: Folder[]): string[] {
  const descendants: string[] = [];
  
  const collectDescendants = (parentId: string) => {
    const children = folders.filter(f => f.parentId === parentId);
    for (const child of children) {
      descendants.push(child.id);
      collectDescendants(child.id);
    }
  };
  
  collectDescendants(folderId);
  return descendants;
}

/**
 * Get the parent ID of an item
 */
function getItemParent(
  itemId: string,
  itemType: 'folder' | 'project' | 'task',
  folders: Folder[],
  projects: Project[],
  tasks: Task[]
): string | null {
  switch (itemType) {
    case 'folder':
      const folder = folders.find(f => f.id === itemId);
      return folder?.parentId || null;
    case 'project':
      const project = projects.find(p => p.id === itemId);
      return project?.folderId || null;
    case 'task':
      const task = tasks.find(t => t.id === itemId);
      return task?.folderId || task?.projectId || null;
    default:
      return null;
  }
}

/**
 * Calculate the new sort order for an item being moved
 */
export function calculateNewSortOrder(
  operation: MoveOperation,
  folders: Folder[],
  projects: Project[],
  tasks: Task[]
): number {
  const { targetId, targetType, position } = operation;
  
  // Get siblings in the target location
  const targetParent = position === 'inside' ? targetId : getItemParent(targetId, targetType, folders, projects, tasks);
  
  // For now, return a simple increment. In a real implementation,
  // you'd calculate based on existing sort orders
  return Date.now();
}

/**
 * Get all items that need to be moved when moving a folder (including all descendants)
 */
export function getItemsToMove(
  folderId: string,
  folders: Folder[],
  projects: Project[],
  tasks: Task[]
): {
  folders: string[];
  projects: string[];
  tasks: string[];
} {
  const descendantFolderIds = getAllDescendantFolderIds(folderId, folders);
  const allFolderIds = [folderId, ...descendantFolderIds];
  
  // Get all projects in these folders
  const projectIds = projects
    .filter(p => p.folderId && allFolderIds.includes(p.folderId))
    .map(p => p.id);
  
  // Get all tasks in these folders or projects
  const taskIds = tasks
    .filter(t => 
      (t.folderId && allFolderIds.includes(t.folderId)) ||
      (t.projectId && projectIds.includes(t.projectId))
    )
    .map(t => t.id);
  
  return {
    folders: descendantFolderIds,
    projects: projectIds,
    tasks: taskIds
  };
}

/**
 * Generate move operations for updating parent relationships
 */
export function generateMoveOperations(
  operation: MoveOperation,
  folders: Folder[],
  projects: Project[],
  tasks: Task[]
): Array<{
  type: 'folder' | 'project' | 'task';
  id: string;
  updates: Record<string, any>;
}> {
  const operations: Array<{
    type: 'folder' | 'project' | 'task';
    id: string;
    updates: Record<string, any>;
  }> = [];
  
  const validation = validateMoveOperation(operation, folders, projects, tasks);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }
  
  const { draggedItem } = operation;
  const { newParentId } = validation;
  
  if (draggedItem.type === 'folder') {
    // Move the folder itself
    operations.push({
      type: 'folder',
      id: draggedItem.id,
      updates: { parentId: newParentId }
    });
    
    // No need to move descendants - they maintain their relationships
  } else if (draggedItem.type === 'project') {
    operations.push({
      type: 'project',
      id: draggedItem.id,
      updates: { folderId: newParentId }
    });
  } else if (draggedItem.type === 'task') {
    // For tasks, we need to determine if it goes to a folder or project
    const updates: Record<string, any> = {};
    
    if (newParentId) {
      const targetFolder = folders.find(f => f.id === newParentId);
      const targetProject = projects.find(p => p.id === newParentId);
      
      if (targetFolder) {
        updates.folderId = newParentId;
        updates.projectId = null;
      } else if (targetProject) {
        updates.projectId = newParentId;
        updates.folderId = null;
      }
    } else {
      // Moving to root
      updates.folderId = null;
      updates.projectId = null;
    }
    
    operations.push({
      type: 'task',
      id: draggedItem.id,
      updates
    });
  }
  
  return operations;
}
