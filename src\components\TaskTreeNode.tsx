import React, { useState } from 'react';
import { ChevronRight, ChevronDown, Folder, FolderOpen, FileText, Plus, MoreVertical, Edit, Copy, Trash2 } from 'lucide-react';
import { Task } from '../types';

interface TaskTreeNodeProps {
  id: string;
  type: 'folder' | 'project' | 'task';
  name: string;
  level: number;
  color?: string;
  status?: Task['status'];
  isExpanded?: boolean;
  isSelected?: boolean;
  onToggle?: () => void;
  onSelect?: () => void;
  onDoubleClick?: () => void;
  onContextAction?: (nodeId: string, action: 'addTask' | 'addProject' | 'addFolder' | 'editProject' | 'editFolder' | 'cloneProject' | 'deleteFolder' | 'deleteProject') => void;
  children?: React.ReactNode;
  // Drag and drop props
  isDragging?: boolean;
  isDragOver?: boolean;
  dropPosition?: 'before' | 'after' | 'inside' | null;
  onDragStart?: (id: string, type: 'folder' | 'project' | 'task') => void;
  onDragOver?: (e: React.DragEvent, targetId: string, targetType: 'folder' | 'project' | 'task') => void;
  onDragLeave?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent, targetId: string, targetType: 'folder' | 'project' | 'task') => void;
  onDragEnd?: () => void;
}

export default function TaskTreeNode({
  id,
  type,
  name,
  level,
  color,
  status,
  isExpanded = false,
  isSelected = false,
  onToggle,
  onSelect,
  onDoubleClick,
  onContextAction,
  children,
  isDragging = false,
  isDragOver = false,
  dropPosition = null,
  onDragStart,
  onDragOver,
  onDragLeave,
  onDrop,
  onDragEnd
}: TaskTreeNodeProps) {
  const [showContextMenu, setShowContextMenu] = useState(false);
  const hasChildren = React.Children.count(children) > 0;

  const getIcon = () => {
    switch (type) {
      case 'folder':
        return isExpanded ? <FolderOpen className="w-4 h-4" /> : <Folder className="w-4 h-4" />;
      case 'project':
        return <div className={`w-3 h-3 rounded-full ${color || 'bg-blue-500'}`} />;
      case 'task':
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'todo': return 'text-gray-500';
      case 'in-progress': return 'text-blue-500';
      case 'done': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getContextMenuItems = () => {
    switch (type) {
      case 'folder':
        return [
          { label: 'Rename Folder', action: 'editFolder' as const, icon: Edit },
          { label: 'Add Project', action: 'addProject' as const, icon: Plus },
          { label: 'Add Subfolder', action: 'addFolder' as const, icon: Plus },
          { label: 'Delete Folder', action: 'deleteFolder' as const, icon: Trash2, danger: true }
        ];
      case 'project':
        return [
          { label: 'Edit Project', action: 'editProject' as const, icon: Edit },
          { label: 'Clone Project', action: 'cloneProject' as const, icon: Copy },
          { label: 'Add Task', action: 'addTask' as const, icon: Plus },
          { label: 'Delete Project', action: 'deleteProject' as const, icon: Trash2, danger: true }
        ];
      case 'task':
        return [];
      default:
        return [];
    }
  };

  // Get drop zone styling
  const getDropZoneClass = () => {
    if (!isDragOver || !dropPosition) return '';

    switch (dropPosition) {
      case 'before':
        return 'border-t-2 border-blue-400';
      case 'after':
        return 'border-b-2 border-blue-400';
      case 'inside':
        return 'bg-blue-600/20 border border-blue-400';
      default:
        return '';
    }
  };

  return (
    <div className="relative">
      {/* Drop indicator for 'before' position */}
      {isDragOver && dropPosition === 'before' && (
        <div className="absolute top-0 left-0 right-0 h-0.5 bg-blue-400 z-10" />
      )}

      <div
        draggable={!!onDragStart}
        onDragStart={() => onDragStart?.(id, type)}
        onDragOver={(e) => onDragOver?.(e, id, type)}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop?.(e, id, type)}
        onDragEnd={onDragEnd}
        className={`flex items-center gap-2 px-2 py-1 rounded cursor-pointer hover:bg-gray-700 group text-white min-w-max transition-all ${
          isSelected ? 'bg-blue-600 text-white' : ''
        } ${isDragging ? 'opacity-50 cursor-grabbing' : 'cursor-grab'} ${getDropZoneClass()}`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={onSelect}
        onDoubleClick={onDoubleClick}
      >
        {/* Expand/collapse button */}
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle?.();
            }}
            className="p-0.5 hover:bg-gray-600 rounded text-white flex-shrink-0"
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </button>
        )}

        {/* Icon */}
        <div className={`flex-shrink-0 ${type === 'task' ? getStatusColor() : ''}`}>
          {getIcon()}
        </div>

        {/* Name - prevent truncation for horizontal scroll */}
        <span className="text-sm text-white whitespace-nowrap">{name}</span>

        {/* Context menu button */}
        {onContextAction && getContextMenuItems().length > 0 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowContextMenu(!showContextMenu);
            }}
            className="p-1 opacity-0 group-hover:opacity-100 hover:bg-gray-600 rounded text-white flex-shrink-0 ml-2"
          >
            <MoreVertical className="w-3 h-3" />
          </button>
        )}
      </div>

      {/* Context menu */}
      {showContextMenu && onContextAction && (
        <div className="absolute right-0 top-8 bg-gray-700 border border-gray-600 rounded-lg shadow-lg py-1 z-50 min-w-[140px]">
          {getContextMenuItems().map((item) => {
            const IconComponent = item.icon || Plus;
            return (
              <button
                key={item.action}
                onClick={(e) => {
                  e.stopPropagation();
                  onContextAction(id, item.action);
                  setShowContextMenu(false);
                }}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-600 flex items-center gap-2 ${
                  (item as any).danger ? 'text-red-400 hover:text-red-300' : 'text-white'
                }`}
              >
                <IconComponent className="w-3 h-3" />
                {item.label}
              </button>
            );
          })}
        </div>
      )}
      
      {/* Drop indicator for 'after' position */}
      {isDragOver && dropPosition === 'after' && (
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 z-10" />
      )}

      {/* Children */}
      {isExpanded && children && (
        <div>
          {children}
        </div>
      )}
    </div>
  );
}



